#!/usr/bin/env python3
"""
Final System Test
=================

Comprehensive test of all system components

Author: Augment Agent
"""

import asyncio
import logging
import sys
import os
import importlib.util
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalSystemTester:
    """Final system testing class"""
    
    def __init__(self):
        self.test_results = {}
        
    def test_file_imports(self):
        """Test if core files can be imported"""
        logger.info("Testing file imports...")
        
        files_to_test = [
            ('Enhanced Market Data', 'src/trading/enhanced_market_data.py'),
            ('Market Analyzer', 'src/trading/market_analyzer.py'),
            ('Trading Handlers', 'src/handlers/trading_handlers.py'),
            ('Subscription System', 'src/services/subscription_system.py'),
            ('API Manager', 'src/api_manager.py'),
            ('Analysis Helpers', 'src/analysis/analysis_helpers.py')
        ]
        
        results = {}
        
        for name, file_path in files_to_test:
            try:
                full_path = os.path.join(project_root, file_path)
                
                if os.path.exists(full_path):
                    # Check file size and basic content
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if len(content) > 100:  # Basic content check
                        results[name] = {'status': 'OK', 'size': len(content)}
                        logger.info(f"✅ {name}: File exists with content ({len(content)} chars)")
                    else:
                        results[name] = {'status': 'TOO_SMALL', 'size': len(content)}
                        logger.warning(f"⚠️ {name}: File too small")
                else:
                    results[name] = {'status': 'FILE_NOT_FOUND', 'path': file_path}
                    logger.error(f"❌ {name}: File not found")
                    
            except Exception as e:
                results[name] = {'status': 'ERROR', 'error': str(e)}
                logger.error(f"❌ {name}: {str(e)}")
        
        return results
    
    async def test_api_connections(self):
        """Test external API connections"""
        logger.info("Testing API connections...")
        
        try:
            import aiohttp
            
            apis_to_test = [
                ('CoinGecko Ping', 'https://api.coingecko.com/api/v3/ping'),
                ('CoinGecko BTC', 'https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd'),
                ('Binance Ping', 'https://api.binance.com/api/v3/ping'),
                ('Binance BTC', 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT')
            ]
            
            results = {}
            
            async with aiohttp.ClientSession() as session:
                for name, url in apis_to_test:
                    try:
                        async with session.get(url, timeout=10) as response:
                            if response.status == 200:
                                data = await response.json()
                                results[name] = {
                                    'status': 'OK', 
                                    'response_code': response.status,
                                    'has_data': bool(data)
                                }
                                logger.info(f"✅ {name}: Success")
                            else:
                                results[name] = {'status': 'HTTP_ERROR', 'response_code': response.status}
                                logger.warning(f"⚠️ {name}: HTTP {response.status}")
                    except asyncio.TimeoutError:
                        results[name] = {'status': 'TIMEOUT'}
                        logger.error(f"❌ {name}: Timeout")
                    except Exception as e:
                        results[name] = {'status': 'ERROR', 'error': str(e)}
                        logger.error(f"❌ {name}: {str(e)}")
            
            return results
            
        except ImportError:
            logger.error("aiohttp not available")
            return {'error': 'aiohttp not installed'}
    
    def test_config_files(self):
        """Test configuration files"""
        logger.info("Testing configuration files...")
        
        config_files = [
            ('Firebase Indexes', 'src/config/firestore.indexes.json'),
            ('Enhanced Market Data', 'src/trading/enhanced_market_data.py'),
            ('Setup Script', 'scripts/setup_firebase.py'),
            ('Auto Index Script', 'scripts/create_indexes_auto.py')
        ]
        
        results = {}
        
        for name, file_path in config_files:
            full_path = os.path.join(project_root, file_path)
            
            if os.path.exists(full_path):
                try:
                    with open(full_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    if content.strip():
                        results[name] = {'status': 'OK', 'size': len(content)}
                        logger.info(f"✅ {name}: OK ({len(content)} chars)")
                    else:
                        results[name] = {'status': 'EMPTY'}
                        logger.warning(f"⚠️ {name}: Empty file")
                except Exception as e:
                    results[name] = {'status': 'READ_ERROR', 'error': str(e)}
                    logger.error(f"❌ {name}: Read error")
            else:
                results[name] = {'status': 'NOT_FOUND'}
                logger.warning(f"⚠️ {name}: Not found")
        
        return results
    
    def test_integration_features(self):
        """Test integration features"""
        logger.info("Testing integration features...")
        
        features_to_check = [
            ('Three Analysis Types', 'enhanced_market_data.py', 'analysis_type'),
            ('User Permission Check', 'enhanced_market_data.py', '_check_user_access'),
            ('Safe Message Edit', 'trading_handlers.py', 'safe_edit_message'),
            ('Analysis Type Info', 'trading_handlers.py', '_get_analysis_type_info'),
            ('Historical Data Types', 'market_analyzer.py', 'analysis_type'),
            ('Firebase Indexes', 'firestore.indexes.json', 'trading_recommendations')
        ]
        
        results = {}
        
        for feature_name, file_name, search_term in features_to_check:
            try:
                # Find the file
                file_found = False
                for root, dirs, files in os.walk(project_root):
                    if file_name in files:
                        file_path = os.path.join(root, file_name)
                        file_found = True
                        break
                
                if file_found:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if search_term in content:
                        results[feature_name] = {'status': 'IMPLEMENTED', 'file': file_name}
                        logger.info(f"✅ {feature_name}: Implemented")
                    else:
                        results[feature_name] = {'status': 'NOT_FOUND_IN_FILE', 'file': file_name}
                        logger.warning(f"⚠️ {feature_name}: Not found in file")
                else:
                    results[feature_name] = {'status': 'FILE_NOT_FOUND', 'file': file_name}
                    logger.error(f"❌ {feature_name}: File not found")
                    
            except Exception as e:
                results[feature_name] = {'status': 'ERROR', 'error': str(e)}
                logger.error(f"❌ {feature_name}: {str(e)}")
        
        return results
    
    def print_final_summary(self, all_results):
        """Print final test summary"""
        logger.info("\n" + "="*60)
        logger.info("FINAL TEST SUMMARY")
        logger.info("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for test_category, results in all_results.items():
            logger.info(f"\n{test_category}:")
            
            if isinstance(results, dict) and 'error' not in results:
                category_passed = 0
                category_total = 0
                
                for item_name, item_result in results.items():
                    category_total += 1
                    total_tests += 1
                    
                    if isinstance(item_result, dict):
                        status = item_result.get('status', 'UNKNOWN')
                        if status in ['OK', 'IMPLEMENTED']:
                            category_passed += 1
                            passed_tests += 1
                            logger.info(f"  ✅ {item_name}")
                        else:
                            logger.info(f"  ❌ {item_name}: {status}")
                
                logger.info(f"  Category Result: {category_passed}/{category_total}")
            else:
                logger.error(f"  ❌ Category failed: {results}")
                total_tests += 1
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info(f"\nOVERALL RESULT: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 90:
            logger.info("🎉 EXCELLENT: System is ready for production!")
            status = "EXCELLENT"
        elif success_rate >= 75:
            logger.info("✅ GOOD: System is mostly ready with minor issues")
            status = "GOOD"
        elif success_rate >= 50:
            logger.info("⚠️ FAIR: System has some issues that need attention")
            status = "FAIR"
        else:
            logger.info("❌ POOR: System has major issues and needs significant work")
            status = "POOR"
        
        return status, success_rate

async def main():
    """Main function"""
    logger.info("Starting final system tests...")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = FinalSystemTester()
    all_results = {}
    
    # Run all tests
    logger.info("\n" + "="*50)
    all_results['File Imports'] = tester.test_file_imports()
    
    logger.info("\n" + "="*50)
    all_results['Configuration Files'] = tester.test_config_files()
    
    logger.info("\n" + "="*50)
    all_results['Integration Features'] = tester.test_integration_features()
    
    logger.info("\n" + "="*50)
    all_results['API Connections'] = await tester.test_api_connections()
    
    # Print final summary
    status, success_rate = tester.print_final_summary(all_results)
    
    logger.info("\n" + "="*60)
    logger.info("RECOMMENDATIONS")
    logger.info("="*60)
    
    if status in ["EXCELLENT", "GOOD"]:
        logger.info("✅ System is ready! Next steps:")
        logger.info("1. Create Firebase indexes: python scripts/create_indexes_auto.py")
        logger.info("2. Test with real users and scenarios")
        logger.info("3. Monitor system performance")
        logger.info("4. Deploy to production")
    else:
        logger.info("⚠️ System needs more work:")
        logger.info("1. Fix failing tests above")
        logger.info("2. Complete missing components")
        logger.info("3. Re-run tests")
        logger.info("4. Consider getting help with specific issues")
    
    return status in ["EXCELLENT", "GOOD"]

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

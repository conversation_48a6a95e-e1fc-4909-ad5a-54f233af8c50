#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد Firebase مبسط
==========================

إنشاء الفهارس المطلوبة لنظام التداول

المؤلف: Augment Agent
"""

import os
import sys
import json
from datetime import datetime

def create_firestore_indexes():
    """إنشاء ملف فهارس Firestore"""
    
    indexes = {
        "indexes": [
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {
                        "fieldPath": "status",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "user_id", 
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at",
                        "order": "DESCENDING"
                    }
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION", 
                "fields": [
                    {
                        "fieldPath": "user_id",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at",
                        "order": "DESCENDING"
                    }
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {
                        "fieldPath": "symbol",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at", 
                        "order": "DESCENDING"
                    }
                ]
            }
        ],
        "fieldOverrides": []
    }
    
    # إنشاء مجلد src/config إذا لم يكن موجوداً
    config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src', 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    # حفظ ملف الفهارس
    indexes_file = os.path.join(config_dir, 'firestore.indexes.json')
    
    with open(indexes_file, 'w', encoding='utf-8') as f:
        json.dump(indexes, f, indent=2, ensure_ascii=False)
    
    print(f"تم إنشاء ملف الفهارس: {indexes_file}")
    return indexes_file

def print_manual_instructions():
    """طباعة تعليمات الإعداد اليدوي"""
    
    print("\n" + "="*60)
    print("تعليمات إنشاء الفهارس يدوياً")
    print("="*60)
    
    print("\n1. انتقل إلى Firebase Console:")
    print("   https://console.firebase.google.com/")
    
    print("\n2. اختر مشروعك")
    
    print("\n3. انتقل إلى Firestore Database > Indexes")
    
    print("\n4. انقر على 'Create Index' وأضف الفهارس التالية:")
    
    indexes_info = [
        {
            "name": "فهرس التوصيات الرئيسي",
            "collection": "trading_recommendations",
            "fields": [
                "status (Ascending)",
                "user_id (Ascending)", 
                "created_at (Descending)"
            ]
        },
        {
            "name": "فهرس توصيات المستخدم",
            "collection": "trading_recommendations",
            "fields": [
                "user_id (Ascending)",
                "created_at (Descending)"
            ]
        },
        {
            "name": "فهرس توصيات العملة",
            "collection": "trading_recommendations", 
            "fields": [
                "symbol (Ascending)",
                "created_at (Descending)"
            ]
        }
    ]
    
    for i, index in enumerate(indexes_info, 1):
        print(f"\n   الفهرس {i}: {index['name']}")
        print(f"   Collection: {index['collection']}")
        print("   Fields:")
        for field in index['fields']:
            print(f"     - {field}")
    
    print("\n5. انتظر حتى يكتمل إنشاء جميع الفهارس")
    print("\n6. تحقق من حالة الفهارس في قسم 'Indexes'")

def main():
    """الدالة الرئيسية"""
    
    print("بدء إعداد Firebase...")
    print(f"الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # إنشاء ملف الفهارس
        indexes_file = create_firestore_indexes()
        print(f"تم إنشاء ملف الفهارس بنجاح: {indexes_file}")
        
        # طباعة التعليمات اليدوية
        print_manual_instructions()
        
        print("\n" + "="*60)
        print("تم الانتهاء من إعداد Firebase!")
        print("="*60)
        print("\nالخطوات التالية:")
        print("1. اتبع التعليمات أعلاه لإنشاء الفهارس يدوياً")
        print("2. شغل الاختبارات للتأكد من عمل النظام")
        print("3. راقب الأداء في Firebase Console")
        
        return True
        
    except Exception as e:
        print(f"خطأ في إعداد Firebase: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

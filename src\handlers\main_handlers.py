"""
معالجات الواجهة الرئيسية - Main Interface Handlers

هذا الملف يحتوي على جميع معالجات الواجهة الرئيسية:
- معالجة النقر على الأزرار
- معالجة الرسائل النصية
- عرض القائمة الرئيسية
- عرض رسالة المساعدة
- معالجة أزرار التعليم
- معالج رسائل المدرس الذكي

تم نقل هذه الدوال من main.py كجزء من خطة إعادة الهيكلة - المرحلة العاشرة
"""

import logging
import re
import time
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ContextTypes
from telegram.constants import ParseMode

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# متغيرات عامة (سيتم تهيئتها من main.py)
db = None
subscription_system = None
api_manager = None
user_states = None
user_settings = None
enhanced_analyzer = None
ca = None
get_text = None
show_main_menu = None
show_language_selection = None
show_terms_and_conditions = None
show_enhanced_analysis_menu = None
show_enhanced_settings = None
show_trading_style_options = None
show_analysis_type_options = None
show_enhanced_analysis_explanation = None
show_analysis_comparison = None
show_upgrade_info = None
show_analysis_type_settings = None
setup_api_keys = None
show_platform_selection = None
show_api_instructions = None
delete_api_keys_ui = None
analyze_symbol = None
analyze_symbol_enhanced = None
process_custom_alert = None
handle_paypal_payment = None
handle_payment_verification = None
manage_free_day_settings = None
set_free_day = None
verify_binance_api = None
verify_gemini_api = None
api_setup_command = None
handle_learn_trading_ai = None
handle_ask_ai_tutor_button = None
generate_and_send_chapter = None
start_quiz = None
show_quiz_results_or_next_steps = None
show_supplementary_chapters = None
generate_and_send_supplementary_chapter = None
handle_message_for_ai_tutor = None
firestore_cache = None

def initialize_main_handlers(
    firestore_db,
    subscription_sys,
    api_mgr,
    user_st,
    user_set,
    enhanced_analyzer_obj,
    crypto_analysis,
    get_text_func,
    show_main_menu_func,
    show_language_selection_func,
    show_terms_and_conditions_func,
    show_enhanced_analysis_menu_func,
    show_enhanced_settings_func,
    show_trading_style_options_func,
    show_analysis_type_options_func,
    show_enhanced_analysis_explanation_func,
    show_analysis_comparison_func,
    show_upgrade_info_func,
    show_analysis_type_settings_func,
    setup_api_keys_func,
    show_platform_selection_func,
    show_api_instructions_func,
    delete_api_keys_ui_func,
    analyze_symbol_func,
    analyze_symbol_enhanced_func,
    process_custom_alert_func,
    handle_paypal_payment_func,
    handle_payment_verification_func,
    manage_free_day_settings_func,
    set_free_day_func,
    verify_binance_api_func,
    verify_gemini_api_func,
    api_setup_command_func,
    handle_learn_trading_ai_func,
    handle_ask_ai_tutor_button_func,
    generate_and_send_chapter_func,
    start_quiz_func,
    show_quiz_results_or_next_steps_func,
    show_supplementary_chapters_func,
    generate_and_send_supplementary_chapter_func,
    handle_message_for_ai_tutor_func,
    firestore_cache_obj
):
    """تهيئة معالجات الواجهة الرئيسية مع جميع التبعيات المطلوبة"""
    global db, subscription_system, api_manager, user_states, user_settings
    global enhanced_analyzer, ca, get_text, show_language_selection
    global show_terms_and_conditions, show_enhanced_analysis_menu, show_enhanced_settings
    global show_trading_style_options, show_analysis_type_options, show_enhanced_analysis_explanation
    global show_analysis_comparison, show_upgrade_info, show_analysis_type_settings
    global setup_api_keys, show_platform_selection, show_api_instructions, delete_api_keys_ui
    global analyze_symbol, analyze_symbol_enhanced, process_custom_alert, handle_paypal_payment
    global handle_payment_verification, manage_free_day_settings, set_free_day
    global verify_binance_api, verify_gemini_api, api_setup_command, handle_learn_trading_ai
    global handle_ask_ai_tutor_button, generate_and_send_chapter, start_quiz
    global show_quiz_results_or_next_steps, show_supplementary_chapters
    global generate_and_send_supplementary_chapter, handle_message_for_ai_tutor, firestore_cache

    db = firestore_db
    subscription_system = subscription_sys
    api_manager = api_mgr
    user_states = user_st
    user_settings = user_set
    enhanced_analyzer = enhanced_analyzer_obj
    ca = crypto_analysis
    get_text = get_text_func
    # لا نعيد تعيين show_main_menu لأنها دالة محلية في هذا الملف
    show_language_selection = show_language_selection_func
    show_terms_and_conditions = show_terms_and_conditions_func
    show_enhanced_analysis_menu = show_enhanced_analysis_menu_func
    show_enhanced_settings = show_enhanced_settings_func
    show_trading_style_options = show_trading_style_options_func
    show_analysis_type_options = show_analysis_type_options_func
    show_enhanced_analysis_explanation = show_enhanced_analysis_explanation_func
    show_analysis_comparison = show_analysis_comparison_func
    show_upgrade_info = show_upgrade_info_func
    show_analysis_type_settings = show_analysis_type_settings_func
    setup_api_keys = setup_api_keys_func
    show_platform_selection = show_platform_selection_func
    show_api_instructions = show_api_instructions_func
    delete_api_keys_ui = delete_api_keys_ui_func
    analyze_symbol = analyze_symbol_func
    analyze_symbol_enhanced = analyze_symbol_enhanced_func
    process_custom_alert = process_custom_alert_func
    handle_paypal_payment = handle_paypal_payment_func
    handle_payment_verification = handle_payment_verification_func
    manage_free_day_settings = manage_free_day_settings_func
    set_free_day = set_free_day_func
    verify_binance_api = verify_binance_api_func
    verify_gemini_api = verify_gemini_api_func
    api_setup_command = api_setup_command_func
    handle_learn_trading_ai = handle_learn_trading_ai_func
    handle_ask_ai_tutor_button = handle_ask_ai_tutor_button_func
    generate_and_send_chapter = generate_and_send_chapter_func
    start_quiz = start_quiz_func
    show_quiz_results_or_next_steps = show_quiz_results_or_next_steps_func
    show_supplementary_chapters = show_supplementary_chapters_func
    generate_and_send_supplementary_chapter = generate_and_send_supplementary_chapter_func
    handle_message_for_ai_tutor = handle_message_for_ai_tutor_func
    firestore_cache = firestore_cache_obj
    
    logger.info("✅ تم تهيئة معالجات الواجهة الرئيسية بنجاح")

async def show_main_menu(update: Update, context: CallbackContext, new_message=False):
    """عرض القائمة الرئيسية"""
    try:
        user_id = str(update.effective_user.id)
        logger.info(f"جاري عرض القائمة الرئيسية للمستخدم {user_id}")

        # الحصول على إعدادات المستخدم لتحديد اللغة
        # إذا لم يكن subscription_system متاحاً، نستخدم اللغة الافتراضية
        lang = 'ar'  # اللغة الافتراضية
        if subscription_system is not None:
            try:
                settings = subscription_system.get_user_settings(user_id)
                lang = settings.get('lang', 'ar') if settings else 'ar'
            except Exception as e:
                logger.warning(f"خطأ في الحصول على إعدادات المستخدم: {str(e)}")
                lang = 'ar'  # التأكد من تعيين اللغة الافتراضية في حالة الخطأ

        # استيراد الدوال المطلوبة
        from utils.text_helpers import get_main_menu_text, get_main_menu_keyboard

        # الحصول على نص ولوحة مفاتيح القائمة الرئيسية
        menu_text = await get_main_menu_text(user_id, lang)
        keyboard = get_main_menu_keyboard(user_id, lang)

        if new_message:
            # إرسال رسالة جديدة
            await update.effective_message.reply_text(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            # تحديث الرسالة الحالية
            await update.callback_query.edit_message_text(
                menu_text,
                reply_markup=keyboard,
                parse_mode=ParseMode.MARKDOWN
            )

        logger.info(f"تم عرض القائمة الرئيسية للمستخدم {user_id} بنجاح")

    except Exception as e:
        logger.error(f"خطأ في عرض القائمة الرئيسية: {str(e)}")
        # محاولة إرسال رسالة خطأ بسيطة
        error_text = "❌ حدث خطأ في عرض القائمة الرئيسية. الرجاء المحاولة مرة أخرى."
        try:
            if new_message:
                await update.effective_message.reply_text(error_text)
            else:
                await update.effective_message.reply_text(error_text)
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ: {str(inner_e)}")

async def help_command(update: Update, context: CallbackContext):
    """عرض رسالة المساعدة"""
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    help_text = get_text('help_text', lang)
    keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await update.message.reply_text(help_text, reply_markup=reply_markup, parse_mode=ParseMode.MARKDOWN)

async def button_click(update: Update, context: CallbackContext):
    """معالجة النقر على الأزرار"""
    try:
        query = update.callback_query
        user_id = str(query.from_user.id)

        # إضافة معالجة أفضل للأخطاء
        try:
            await query.answer()  # إجابة سريعة لتجنب timeout
        except Exception as answer_error:
            logger.warning(f"فشل في إرسال إجابة سريعة: {str(answer_error)}")
            # المتابعة بدون إيقاف المعالجة

        # حماية: التأكد من تهيئة api_manager أيضًا
        if api_manager is None:
            logger.error("api_manager غير متوفر")
            await query.answer("❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً", show_alert=True)
            return

        # تطبيق middleware الأمان المتقدم
        try:
            # الحصول على نظام الأمان المتقدم
            from core.system_initialization_extended import _system_components_cache
            if _system_components_cache and 'api_security' in _system_components_cache:
                api_security = _system_components_cache['api_security']

                # التحقق من أمان الطلب
                is_safe, reason, details = await api_security.validate_request(
                    endpoint='button_click',
                    user_id=user_id,
                    ip_address=None,  # Telegram لا يوفر IP
                    headers={},
                    data={'callback_data': query.data}
                )

                if not is_safe:
                    logger.warning(f"🚫 طلب محظور من المستخدم {user_id}: {reason}")
                    await query.answer("❌ طلب غير مسموح", show_alert=True)
                    return

        except Exception as security_error:
            logger.warning(f"خطأ في فحص الأمان: {str(security_error)}")
            # المتابعة بدون فحص الأمان في حالة الخطأ

        # تم حذف مراقبة الأداء لأن الاستضافة تتولى هذه المهام
        success = False

        # التحقق من حظر المستخدم
        banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await query.answer("⛔️ عذراً، تم حظر حسابك من استخدام البوت", show_alert=True)
            return

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await query.answer("❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً", show_alert=True)
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # معالجة اختيار اللغة الأولي
        if query.data.startswith('set_initial_lang_'):
            selected_lang = query.data.split('_')[-1]  # ar أو en
            logger.info(f"المستخدم {user_id} اختار اللغة الأولية: {selected_lang}")

            # تحديث إعدادات المستخدم مع اللغة المختارة
            success = subscription_system.update_user_settings(user_id, lang=selected_lang, lang_selected=True)
            if success:
                logger.info(f"✅ تم تحديث لغة المستخدم {user_id} إلى {selected_lang} وتسجيل اختيار اللغة")

                # إرسال رسالة تأكيد اختيار اللغة
                confirm_message = "تم اختيار اللغة العربية بنجاح" if selected_lang == 'ar' else "English language selected successfully"
                await query.answer(confirm_message)

                # عرض الشروط والأحكام بعد اختيار اللغة
                logger.info(f"🔄 الانتقال إلى عرض الشروط والأحكام للمستخدم {user_id} باللغة {selected_lang}")
                await show_terms_and_conditions(update, context, selected_lang)
            else:
                logger.error(f"❌ فشل في حفظ إعدادات اللغة للمستخدم {user_id}")
                error_message = "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى" if selected_lang == 'ar' else "Error saving settings. Please try again"
                await query.answer(error_message, show_alert=True)
            return

        # معالجة الموافقة على الشروط والأحكام
        elif query.data == 'terms_agree':
            logger.info(f"المستخدم {user_id} يحاول الموافقة على الشروط والأحكام")

            # تحديث إعدادات المستخدم لتسجيل الموافقة على الشروط
            success = subscription_system.update_user_settings(user_id, terms_accepted=True)
            if success:
                logger.info(f"✅ تم تسجيل موافقة المستخدم {user_id} على الشروط والأحكام بنجاح")

                # رسالة ترحيبية مفصلة للمستخدمين الجدد
                if lang == 'ar':
                    welcome_text = """🎉 **مرحباً بك في بوت التحليل الفني!**

✅ تم قبول الشروط والأحكام بنجاح

🚀 **ما يمكنك فعله الآن:**
• 🤖 نظام التداول الآلي الذكي
• 📊 تحليل العملات الرقمية
• 📈 مراقبة الأسعار والاتجاهات
• 🎓 تعلم أساسيات التداول
• 💡 الحصول على توصيات فنية

🎁 **هدية الترحيب:**
تم منحك يوم مجاني للاستمتاع بجميع الميزات المدفوعة!

اختر من القائمة أدناه للبدء:"""
                else:
                    welcome_text = """🎉 **Welcome to the Technical Analysis Bot!**

✅ Terms and conditions accepted successfully

🚀 **What you can do now:**
• 🤖 Smart Automated Trading System
• 📊 Analyze cryptocurrencies
• 📈 Monitor prices and trends
• 🎓 Learn trading basics
• 💡 Get technical recommendations

🎁 **Welcome Gift:**
You've been granted a free day to enjoy all premium features!

Choose from the menu below to get started:"""

                # إرسال رسالة الترحيب المفصلة
                try:
                    await query.edit_message_text(
                        text=welcome_text,
                        parse_mode=ParseMode.MARKDOWN
                    )
                    logger.info(f"✅ تم عرض رسالة الترحيب للمستخدم الجديد {user_id}")
                except Exception as edit_error:
                    logger.warning(f"فشل في تحديث رسالة الترحيب، إرسال رسالة جديدة: {str(edit_error)}")
                    await query.message.reply_text(
                        text=welcome_text,
                        parse_mode=ParseMode.MARKDOWN
                    )

                # إرسال القائمة الرئيسية بعد ثانيتين
                await asyncio.sleep(2)
                await show_main_menu(update, context, new_message=True)
                logger.info(f"تم عرض القائمة الرئيسية للمستخدم {user_id} بعد الموافقة على الشروط")
            else:
                # رسالة خطأ في حالة فشل تحديث الإعدادات
                logger.error(f"❌ فشل في تسجيل موافقة المستخدم {user_id} على الشروط والأحكام")
                error_message = "حدث خطأ أثناء حفظ الموافقة. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "Error saving agreement. Please try again"
                await query.answer(error_message, show_alert=True)
            return

        # معالجة رفض الشروط والأحكام
        elif query.data == 'terms_decline':
            logger.info(f"❌ المستخدم {user_id} رفض الشروط والأحكام - لا يمكنه استخدام البوت")

            # استخدام نصوص ثابتة بدلاً من قاموس الترجمة
            decline_message = "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت" if lang == 'ar' else "Sorry, you must agree to the terms and conditions to use the bot"
            required_message = "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت. يمكنك إعادة تشغيل البوت في أي وقت للموافقة على الشروط. 🔄" if lang == 'ar' else "Sorry, you must agree to the terms and conditions to use the bot. You can restart the bot at any time to agree to the terms. 🔄"

            # إرسال رسالة تنبيه ورسالة توضيحية
            await query.answer(decline_message, show_alert=True)
            await query.message.reply_text(required_message)
            logger.info(f"تم إرسال رسالة رفض الشروط للمستخدم {user_id}")
            return

        # معالجة زر الشروط والأحكام من القائمة الرئيسية
        elif query.data == 'terms':
            logger.info(f"المستخدم {user_id} نقر على زر الشروط والأحكام")
            try:
                # عرض الشروط والأحكام
                await show_terms_and_conditions(update, context, lang)
            except Exception as e:
                logger.error(f"خطأ في عرض الشروط والأحكام: {str(e)}")
                await query.answer(
                    "❌ حدث خطأ أثناء عرض الشروط والأحكام" if lang == 'ar' else "❌ Error displaying terms and conditions",
                    show_alert=True
                )
            return

        # معالجة إضافة العملة
        elif query.data.startswith('add_currency_'):
            currency_code = query.data.split('_')[2]

            # تحميل الإعدادات الحالية
            custom_currencies = settings.get('currencies', [])

            # إضافة العملة إذا لم تكن موجودة
            if currency_code not in custom_currencies:
                custom_currencies.append(currency_code)

                # حفظ التغييرات
                settings['currencies'] = custom_currencies
                if subscription_system.update_user_settings(user_id, currencies=custom_currencies):
                    await query.answer(
                        get_text('currency_added', lang).format(currency=currency_code),
                        show_alert=True
                    )
                else:
                    await query.answer(
                        get_text('error_saving_currency', lang),
                        show_alert=True
                    )
            else:
                await query.answer(
                    get_text('currency_exists', lang),
                    show_alert=True
                )

            # العودة إلى قائمة إدارة العملات
            await subscription_system.manage_currencies(update, context)
            return

        # معالجة حذف العملة
        elif query.data.startswith('remove_currency_'):
            currency_code = query.data.split('_')[2]

            # تحميل الإعدادات الحالية
            custom_currencies = settings.get('currencies', [])

            # حذف العملة إذا كانت موجودة
            if currency_code in custom_currencies:
                custom_currencies.remove(currency_code)

                # حفظ التغييرات
                settings['currencies'] = custom_currencies
                if subscription_system.update_user_settings(user_id, currencies=custom_currencies):
                    await query.answer(
                        get_text('currency_removed', lang).format(currency=currency_code),
                        show_alert=True
                    )
                else:
                    await query.answer(
                        get_text('error_saving_currency', lang),
                        show_alert=True
                    )

            # العودة إلى قائمة إدارة العملات
            await subscription_system.manage_currencies(update, context)
            return

        # معالجة تبديل المؤشرات
        if query.data.startswith('toggle_indicator_'):
            parts = query.data.split('_')
            if len(parts) >= 4:
                symbol = parts[2]
                indicator_id = parts[3]

                # تحميل الإعدادات الحالية
                from services.data_manager import load_user_settings, save_user_settings
                settings = load_user_settings(user_id)
                indicators = settings.get('indicators', [])

                # التحقق مما إذا كان المؤشر موجوداً
                is_active = any(ind.get('id') == indicator_id for ind in indicators)

                if is_active:
                    # إزالة المؤشر
                    indicators = [ind for ind in indicators if ind.get('id') != indicator_id]
                    action = get_text('removed', lang)
                else:
                    # إضافة المؤشر
                    indicators.append({'id': indicator_id})
                    action = get_text('added', lang)

                # حفظ التغييرات
                if save_user_settings(user_id, indicators=indicators):
                    # إرسال إشعار popup سريع
                    indicator_name = get_text(f'{indicator_id}_indicator', lang)
                    notification_text = "تم تحديث المؤشرات" if lang == 'ar' else "Indicators updated"
                    await query.answer(text=notification_text, show_alert=False, cache_time=1)

                    # تحديث التحليل
                    await analyze_symbol(update, context, symbol)
                else:
                    await query.answer(text=get_text('error_saving_indicator', lang), show_alert=False, cache_time=1)
                return

        # معالجة التنبيهات النشطة
        elif query.data == 'active_alerts':
            # جلب التنبيهات النشطة من Firestore
            alerts_ref = db.collection('alerts').document(user_id)
            alerts_data = alerts_ref.get()

            if not alerts_data.exists or not alerts_data.to_dict():
                # لا توجد تنبيهات نشطة
                keyboard = [
                    [InlineKeyboardButton(get_text('add_new_alert', lang), callback_data='analyze')],
                    [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
                ]
                no_alerts_text = (
                    "⚠️ لا توجد تنبيهات نشطة حالياً (سيتم استخدام تحليل واحد اذا قمت بالنقر على تنبيه جديد)" if lang == 'ar' else
                    "⚠️ No active alerts (This will use one analysis)"
                )
                await query.edit_message_text(
                    text=no_alerts_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )
                return

            # تجميع التنبيهات النشطة
            alerts = alerts_data.to_dict()
            alerts_text = f"🔔 {get_text('active_alerts_title', lang)}\n\n"

            # عرض عدد التنبيهات النشطة
            total_alerts = sum(len(symbol_alerts) for symbol_alerts in alerts.values())
            alerts_text += f"📊 {get_text('total_alerts', lang)}: {total_alerts}\n\n"

            # تنظيم التنبيهات حسب العملة
            for symbol, symbol_alerts in alerts.items():
                alerts_text += f"💱 *{symbol}*\n"
                for i, alert in enumerate(symbol_alerts, 1):
                    condition = get_text('alert_condition_above', lang) if alert['condition'] == 'above' else get_text('alert_condition_below', lang)
                    alerts_text += f"{i}. {condition} {alert['price']:.4f}\n"
                alerts_text += "\n"

            # إضافة أزرار التحكم
            keyboard = [
                [InlineKeyboardButton(get_text('add_new_alert', lang), callback_data='analyze')],
                [InlineKeyboardButton(get_text('clear_alerts', lang), callback_data='clear_alerts')],
                [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
            ]

            await query.edit_message_text(
                text=alerts_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
            return

        elif query.data == 'clear_alerts':
            # حذف جميع التنبيهات
            alerts_ref = db.collection('alerts').document(user_id)
            alerts_ref.delete()

            # إعادة تعيين عدد التنبيهات المجانية
            if not subscription_system.is_subscribed(user_id):
                usage_ref = db.collection('free_usage').document(user_id)
                current_usage = usage_ref.get().to_dict()
                if current_usage:
                    current_usage['alerts'] = 1
                    current_usage['date'] = datetime.now().date().isoformat()
                    usage_ref.set(current_usage)

                    # تحديث الذاكرة المحلية
                    current_time = datetime.now()
                    subscription_system._free_usage_cache[user_id] = current_usage
                    subscription_system._free_usage_expiry[user_id] = current_time + timedelta(hours=1)

            await query.answer(get_text('alerts_cleared', lang))
            await show_main_menu(update, context)
            return

        if query.data == 'upgrade' or query.data == 'upgrade_account':
            # عرض معلومات الترقية
            await show_upgrade_info(update, context)
            return

        elif query.data == 'payment_paypal':
            # معالجة الدفع عبر PayPal
            try:
                await handle_paypal_payment(update, context)
            except Exception as e:
                logger.error(f"خطأ في معالجة الدفع عبر PayPal: {str(e)}")
                await query.answer("❌ حدث خطأ أثناء معالجة الدفع. الرجاء المحاولة مرة أخرى.", show_alert=True)
                # إعادة توجيه المستخدم إلى القائمة الرئيسية
                await show_main_menu(update, context, new_message=True)
            return

        elif query.data == 'verify_payment' or query.data.startswith('verify_payment_'):
            # التحقق من الدفع
            await handle_payment_verification(update, context)
            return

        elif query.data == 'language':
            # إنشاء أزرار اختيار اللغة
            keyboard = [
                [
                    InlineKeyboardButton("🇸🇦 العربية", callback_data='set_lang_ar'),
                    InlineKeyboardButton("🇬🇧 English", callback_data='set_lang_en')
                ],
                [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
            ]
            await query.edit_message_text(
                text=get_text('choose_language', lang),
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        elif query.data == 'free_day_settings':
            # إدارة إعدادات اليوم المجاني
            await manage_free_day_settings(update, context)
            return

        elif query.data.startswith('set_free_day_'):
            # تعيين اليوم المجاني
            await set_free_day(update, context)
            return

        elif query.data.startswith('set_lang_'):
            # تغيير اللغة - استخدام النظام المحسن
            new_lang = query.data.split('_')[2]

            # التأكد من أن اللغة صحيحة
            if new_lang not in ['ar', 'en']:
                await query.answer("❌ لغة غير مدعومة", show_alert=True)
                return

            # تحديث اللغة باستخدام النظام الموحد
            success = subscription_system.update_user_settings(
                user_id,
                lang=new_lang,
                language=new_lang,
                lang_selected=True,
                updated_at=datetime.now().isoformat()
            )

            if success:
                # تزامن اللغة عبر جميع المجموعات
                try:
                    # تحديث user_settings مباشرة أيضاً
                    settings_ref = db.collection('user_settings').document(user_id)
                    settings_ref.set({
                        'lang': new_lang,
                        'language': new_lang,
                        'lang_selected': True,
                        'updated_at': datetime.now().isoformat()
                    }, merge=True)

                    # تحديث notification_preferences
                    notif_ref = db.collection('notification_preferences').document(user_id)
                    notif_ref.set({
                        'language': new_lang,
                        'lang': new_lang,
                        'updated_at': datetime.now().isoformat()
                    }, merge=True)

                    logger.info(f"✅ تم تزامن لغة المستخدم {user_id} إلى {new_lang}")

                except Exception as e:
                    logger.error(f"خطأ في تزامن اللغة للمستخدم {user_id}: {str(e)}")

                # تحديث الذاكرة المؤقتة
                updated_settings = settings.copy()
                updated_settings.update({
                    'lang': new_lang,
                    'language': new_lang,
                    'lang_selected': True
                })
                firestore_cache.set(f'settings_{user_id}', updated_settings, ex=subscription_system.cache_timeout, cache_type="user_data")

                # إرسال رسالة تأكيد باللغة الجديدة
                success_message = "✅ Language changed to English!" if new_lang == 'en' else "✅ تم تغيير اللغة إلى العربية!"
                await query.answer(success_message)

                # تحديث القائمة الرئيسية باللغة الجديدة
                await show_main_menu(update, context)
            else:
                await query.answer("❌ حدث خطأ في تغيير اللغة", show_alert=True)
            return

        # تم إزالة معالج الأخبار اليدوي - النظام يعمل تلقائياً الآن

        elif query.data == 'back_to_main' or query.data == 'main_menu':
            await show_main_menu(update, context)
            return

        elif query.data == 'setup_api_keys':
            await setup_api_keys(update, context, api_manager, subscription_system)
            return

        elif query.data == 'select_platform':
            await show_platform_selection(update, context, api_manager, subscription_system)
            return

        elif query.data == 'setup_binance_api':
            await show_api_instructions(update, context, 'binance', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'binance_key'}
            return

        elif query.data == 'setup_gemini_api':
            # Gemini متاح لجميع المستخدمين للتعلم مع الذكاء الاصطناعي
            await show_api_instructions(update, context, 'gemini', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'gemini_key'}
            return

        elif query.data == 'setup_kucoin_api':
            await show_api_instructions(update, context, 'kucoin', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'kucoin_key'}
            return

        elif query.data == 'setup_coinbase_api':
            await show_api_instructions(update, context, 'coinbase', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'coinbase_key'}
            return

        elif query.data == 'setup_bybit_api':
            await show_api_instructions(update, context, 'bybit', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'bybit_key'}
            return

        elif query.data == 'setup_okx_api':
            await show_api_instructions(update, context, 'okx', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'okx_key'}
            return

        elif query.data == 'setup_kraken_api':
            await show_api_instructions(update, context, 'kraken', lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': 'kraken_key'}
            return

        elif query.data == 'delete_api_keys':
            # عرض واجهة حذف مفاتيح API
            await delete_api_keys_ui(update, context, api_manager, subscription_system)
            return

        elif query.data.startswith('delete_') and query.data.endswith('_api'):
            # معالجة حذف مفتاح API محدد
            platform = query.data.replace('delete_', '').replace('_api', '')

            # حذف مفاتيح API للمنصة المحددة
            success = await api_manager.delete_api_keys(user_id, platform)

            if success:
                success_message = f"✅ تم حذف مفاتيح {platform.capitalize()} API بنجاح!" if lang == 'ar' else f"✅ {platform.capitalize()} API keys deleted successfully!"
                await query.answer(success_message, show_alert=True)
            else:
                error_message = f"❌ فشل في حذف مفاتيح {platform.capitalize()} API" if lang == 'ar' else f"❌ Failed to delete {platform.capitalize()} API keys"
                await query.answer(error_message, show_alert=True)

            # العودة إلى واجهة حذف مفاتيح API
            await delete_api_keys_ui(update, context, api_manager, subscription_system)
            return

        elif query.data == 'enhanced_analysis_menu':
            # عرض قائمة النظام المحسن
            await show_enhanced_analysis_menu(update, context)
            return

        elif query.data == 'enhanced_analysis_explanation':
            # عرض شرح ميزة التحليل المحسن
            await show_enhanced_analysis_explanation(update, context)
            return

        elif query.data == 'compare_analysis_types':
            # عرض مقارنة بين أنواع التحليل
            await show_analysis_comparison(update, context)
            return

        elif query.data == 'regular_analysis_menu':
            # العودة للتحليل العادي
            await show_main_menu(update, context)
            return

        elif query.data == 'enhanced_analyze' or query.data == 'start_enhanced_analysis':
            # تحليل عملة باستخدام النظام المحسن (دائماً يستخدم التحليل المحسن)
            await query.message.reply_text(
                "أدخل رمز العملة للتحليل المحسن:" if lang == 'ar' else "Enter currency symbol for enhanced analysis:",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
                ]])
            )
            user_states[user_id] = {'state': 'waiting_for_enhanced_symbol', 'analysis_mode': 'enhanced'}
            await query.answer()
            return

        # تم إزالة معالجات أنماط التحليل المنفصلة - النظام الموحد لا يحتاجها

        elif query.data == 'explain_unified_system':
            # شرح النظام الموحد الجديد
            if lang == 'ar':
                explanation = """🚀 **النظام الموحد للتحليل المحسن**

🔄 **ما هو النظام الموحد؟**
نظام تحليل متطور يجمع جميع أنماط التحليل الأربعة في تحليل واحد شامل:

📊 **الأنماط المدمجة:**
• 🏃 **المضاربة السريعة**: مؤشرات الزخم والإشارات السريعة
• 📈 **التداول اليومي**: اتباع الاتجاه والكسر
• 🌊 **التداول المتأرجح**: انعكاس الاتجاه والأنماط
• 🏛️ **الاستثمار طويل المدى**: القيمة والاتجاهات الكبرى

⚡ **المزايا:**
✅ تحليل شامل بنقرة واحدة
✅ دقة أعلى من دمج جميع الاستراتيجيات
✅ توصيات متوازنة تراعي جميع الأطر الزمنية
✅ تقييم شامل للمخاطر من جميع الزوايا
✅ لا حاجة لاختيار نمط محدد

🎯 **النتيجة:** تحليل أكثر دقة وشمولية!"""
            else:
                explanation = """🚀 **Unified Enhanced Analysis System**

🔄 **What is the Unified System?**
Advanced analysis system that combines all four analysis patterns into one comprehensive analysis:

📊 **Integrated Patterns:**
• 🏃 **Scalping**: Momentum indicators and quick signals
• 📈 **Day Trading**: Trend following and breakouts
• 🌊 **Swing Trading**: Trend reversal and patterns
• 🏛️ **Position Trading**: Value and major trends

⚡ **Benefits:**
✅ Comprehensive analysis with one click
✅ Higher accuracy from combining all strategies
✅ Balanced recommendations considering all timeframes
✅ Comprehensive risk assessment from all angles
✅ No need to choose specific pattern

🎯 **Result:** More accurate and comprehensive analysis!"""

            await query.message.reply_text(
                explanation,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data='enhanced_analysis_menu'
                    )
                ]]),
                parse_mode='Markdown'
            )
            await query.answer()
            return

        elif query.data == 'analysis_type_settings' or query.data == 'show_analysis_type_settings':
            # عرض إعدادات نوع التحليل
            await show_analysis_type_settings(update, context)
            return

        elif query.data == 'enhanced_settings':
            # إعدادات النظام المحسن
            await show_enhanced_settings(update, context)
            return

        elif query.data == 'show_trading_style_options':
            # عرض خيارات أنماط التداول
            await show_trading_style_options(update, context)
            return

        elif query.data == 'show_analysis_type_options':
            # عرض خيارات أنواع التحليل
            await show_analysis_type_options(update, context)
            return

        elif query.data == 'change_trading_style':
            # تغيير نمط التداول
            await show_trading_style_options(update, context)
            return

        elif query.data == 'change_analysis_type':
            # تغيير نوع التحليل
            await show_analysis_type_options(update, context)
            return

        elif query.data == 'reset_enhanced_settings':
            # إعادة تعيين الإعدادات
            from handlers.settings_handlers import reset_enhanced_settings
            await reset_enhanced_settings(update, context)
            return

        elif query.data.startswith('set_trading_style_'):
            # تعيين نمط التداول
            style = query.data.replace('set_trading_style_', '')
            from handlers.settings_handlers import set_trading_style
            await set_trading_style(update, context, style)
            return

        elif query.data.startswith('set_analysis_type_'):
            # تعيين نوع التحليل
            analysis_type = query.data.replace('set_analysis_type_', '')
            from handlers.settings_handlers import set_analysis_type
            await set_analysis_type(update, context, analysis_type)
            return

        elif query.data == 'ai_chat':
            # بدء الدردشة مع الذكاء الاصطناعي
            # التحقق من حالة الاشتراك أو اليوم المجاني
            is_premium = subscription_system.is_subscribed_sync(user_id)
            is_free_day = False
            try:
                if subscription_system.free_day_system:
                    is_free_day_today = subscription_system.free_day_system.is_today_free_day(user_id)
                    is_free_day_active = subscription_system.free_day_system.is_free_day_active(user_id)
                    is_free_day = is_free_day_today or is_free_day_active
            except Exception as e:
                logger.error(f"خطأ في التحقق من اليوم المجاني: {str(e)}")
                is_free_day = False

            if not (is_premium or is_free_day):
                await query.answer(
                    "هذه الميزة متاحة للمشتركين أو من لديهم يوم مجاني فقط" if lang == 'ar' else
                    "This feature is available for subscribers or free day users only",
                    show_alert=True
                )
                return

            # التحقق من وجود مفتاح Gemini API
            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
            if not has_gemini_api:
                await query.answer(
                    "يرجى إضافة مفتاح Gemini API الخاص بك أولاً" if lang == 'ar' else
                    "Please add your Gemini API key first",
                    show_alert=True
                )
                # توجيه المستخدم لإعداد المفتاح
                from services.api_management import api_setup_command
                await api_setup_command(update, context, preselect_platform='gemini')
                return

            # تعيين حالة المستخدم للدردشة مع الذكاء الاصطناعي
            user_states[user_id] = {'state': 'ai_chat'}

            # إرسال رسالة ترحيبية
            welcome_message = (
                "🤖 مرحباً بك في الدردشة مع الذكاء الاصطناعي!\n\n"
                "يمكنك الآن طرح أي أسئلة متعلقة بالتداول والعملات الرقمية، وسأقوم بالإجابة عليها بناءً على خبرتي في التحليل الفني والأساسي.\n\n"
                "💡 أمثلة على الأسئلة:\n"
                "• ما رأيك في عملة BTC الآن؟\n"
                "• كيف أحلل مؤشر RSI؟\n"
                "• ما هي أفضل استراتيجية للتداول اليومي؟\n\n"
                "اكتب سؤالك الآن..."
            ) if lang == 'ar' else (
                "🤖 Welcome to AI Chat!\n\n"
                "You can now ask any questions related to trading and cryptocurrencies, and I will answer them based on my expertise in technical and fundamental analysis.\n\n"
                "💡 Example questions:\n"
                "• What do you think about BTC now?\n"
                "• How do I analyze RSI indicator?\n"
                "• What's the best strategy for day trading?\n\n"
                "Write your question now..."
            )

            keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
            await query.edit_message_text(
                welcome_message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            await query.answer()
            success = True
            return

        # تسجيل نجاح العملية
        success = True

    except Exception as e:
        logger.error(f"خطأ في معالجة النقر على الأزرار: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # محاولة إرسال رسالة خطأ للمستخدم
        try:
            if 'query' in locals() and query:
                await query.answer("❌ حدث خطأ. الرجاء المحاولة مرة أخرى.", show_alert=True)
            elif 'update' in locals() and update and update.effective_message:
                await update.effective_message.reply_text("❌ حدث خطأ. الرجاء المحاولة مرة أخرى.")
        except Exception as error_send_error:
            logger.error(f"فشل في إرسال رسالة الخطأ: {str(error_send_error)}")
            pass

    finally:
        # تم حذف تسجيل الأداء لأن الاستضافة تتولى هذه المهام
        pass

async def handle_message(update: Update, context: CallbackContext):
    """معالجة الرسائل النصية"""
    user_id = None
    lang = 'ar'  # افتراضي

    try:
        user_id = str(update.effective_user.id)
        message_text = update.message.text.strip()

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await update.message.reply_text("❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً")
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حظر المستخدم
        banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await update.message.reply_text("⛔️ عذراً، تم حظر حسابك من استخدام البوت. يرجى التواصل مع المطور إذا كنت تعتقد أن هذا خطأ.")
            return

        # فحص حالة التعلم مع الذكاء الاصطناعي أولاً
        from education.trading_education import user_education_state, handle_message_for_ai_tutor
        if user_id in user_education_state and user_education_state[user_id].get('is_asking_ai'):
            logger.info(f"User {user_id} is in 'is_asking_ai' state, redirecting to AI tutor handler")
            await handle_message_for_ai_tutor(update, context)
            return

        # التحقق من حالة المستخدم للدردشة مع الذكاء الاصطناعي
        if user_id in user_states and isinstance(user_states[user_id], dict) and user_states[user_id].get('state') == 'ai_chat':
            # التحقق من حالة الاشتراك أو اليوم المجاني
            is_premium = subscription_system.is_subscribed_sync(user_id)
            is_free_day = False
            try:
                if subscription_system.free_day_system:
                    is_free_day_today = subscription_system.free_day_system.is_today_free_day(user_id)
                    is_free_day_active = subscription_system.free_day_system.is_free_day_active(user_id)
                    is_free_day = is_free_day_today or is_free_day_active
            except Exception as e:
                logger.error(f"خطأ في التحقق من اليوم المجاني: {str(e)}")
                is_free_day = False

            if not (is_premium or is_free_day):
                await update.message.reply_text(
                    "هذه الميزة متاحة للمشتركين أو من لديهم يوم مجاني فقط" if lang == 'ar' else
                    "This feature is available for subscribers or free day users only"
                )
                user_states.pop(user_id, None)
                return

            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
            if not has_gemini_api:
                await update.message.reply_text(
                    "يرجى إضافة مفتاح Gemini API الخاص بك أولاً" if lang == 'ar' else
                    "Please add your Gemini API key first"
                )
                user_states.pop(user_id, None)
                return

            # إرسال رسالة انتظار
            wait_message = await update.message.reply_text(
                "جاري التفكير..." if lang == 'ar' else
                "Thinking..."
            )

            try:
                # استدعاء وظيفة الدردشة مع الذكاء الاصطناعي
                from ai_chat import chat_with_ai
                response = await chat_with_ai(user_id, message_text, lang)

                if response:
                    # إرسال رد الذكاء الاصطناعي مع تنسيق Markdown
                    keyboard = [[InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]]
                    try:
                        await wait_message.edit_text(
                            text=response,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN,
                            disable_web_page_preview=True
                        )
                    except Exception as markdown_error:
                        logger.warning(f"فشل في إرسال رد الذكاء الاصطناعي مع Markdown للمستخدم {user_id}: {markdown_error}")
                        # محاولة إرسال بدون تنسيق Markdown
                        try:
                            await wait_message.edit_text(
                                text=response,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                disable_web_page_preview=True
                            )
                        except Exception as plain_error:
                            logger.error(f"فشل في إرسال رد الذكاء الاصطناعي حتى بدون Markdown للمستخدم {user_id}: {plain_error}")
                            # إرسال رسالة خطأ بسيطة
                            await wait_message.edit_text(
                                "عذراً، حدث خطأ في عرض الرد. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                                "Sorry, an error occurred while displaying the response. Please try again."
                            )
                else:
                    await wait_message.edit_text(
                        "عذراً، حدث خطأ أثناء الاتصال بالذكاء الاصطناعي. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                        "Sorry, an error occurred while connecting to AI. Please try again."
                    )
            except Exception as ai_error:
                logger.error(f"خطأ في الدردشة مع الذكاء الاصطناعي: {str(ai_error)}")
                await wait_message.edit_text(
                    "عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                    "Sorry, an error occurred while processing your request. Please try again."
                )

            # الاستمرار في حالة الدردشة
            return

        # تهيئة الإعدادات إذا لم تكن موجودة
        if user_id not in user_settings:
            user_settings[user_id] = {
                'lang': lang,  # استخدام اللغة المحددة من قاعدة البيانات
                'currencies': [],
                'indicators': []
            }

        settings = user_settings[user_id]
        # استخدام اللغة المحددة من قاعدة البيانات بدلاً من user_settings المحلي
        # lang = settings.get('lang', 'ar')  # تم تعطيل هذا السطر

        # التحقق من حالة المستخدم
        if user_id in user_states:
            state_data = user_states[user_id]
            if isinstance(state_data, dict):
                state = state_data.get('state')

                # معالجة إدخال مفاتيح API
                if 'api_setup_state' in state_data:
                    await _handle_api_setup(update, context, user_id, message_text, state_data, lang)
                    return

            else:
                state = state_data

            if state == 'waiting_for_custom_alert':
                await process_custom_alert(update, context)
                return

            elif state == 'waiting_for_custom_interval':
                # تم إزالة معالجة إدخال فترة مخصصة للتقارير الدورية
                # إعادة المستخدم إلى القائمة الرئيسية
                await update.message.reply_text(
                    "تم إلغاء هذه الميزة" if lang == 'ar' else "This feature has been removed",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')]
                    ])
                )
                # مسح حالة المستخدم
                if user_id in user_states:
                    del user_states[user_id]
                return

            elif state == 'waiting_for_symbol':
                # تنظيف رمز العملة
                symbol = message_text.upper().replace('/', '')

                # التحقق من نوع التحليل المطلوب
                user_state = user_states.get(user_id, {})
                analysis_mode = user_state.get('analysis_mode', 'regular') if isinstance(user_state, dict) else 'regular'

                # تحليل العملة حسب النوع المطلوب
                if analysis_mode == 'enhanced':
                    # استخدام التحليل المحسن مباشرة
                    await analyze_symbol_enhanced(update, context, symbol)
                else:
                    # استخدام التحليل العادي (يحترم إعدادات المستخدم)
                    await analyze_symbol(update, context, symbol)
                return

            elif state == 'waiting_for_enhanced_symbol':
                await _handle_enhanced_symbol_analysis(update, context, user_id, message_text, settings, lang)
                return

        # التحقق من رمز العملة (لرسائل خارج سياق الحالات)
        if re.match(r'^[A-Za-z0-9/\-_.]{1,15}$', message_text):
            # إزالة الرموز الخاصة وتحويل إلى الأحرف الكبيرة
            symbol = re.sub(r'[^A-Za-z0-9]', '', message_text.upper())

            # التحقق من وجود حروف في الرمز
            if any(c.isalpha() for c in symbol):
                # رسالة انتظار محسنة مع مؤشر تقدم
                loading_message = await update.message.reply_text(
                    f"⏳ {get_text('analyzing_market', lang)}\n🔄 جاري جلب البيانات..." if lang == 'ar' else
                    f"⏳ {get_text('analyzing_market', lang)}\n🔄 Fetching data..."
                )

                # إرسال رمز العملة لتحليله
                await analyze_symbol(update, context, symbol, loading_message)
                return

        # إذا لم يكن هناك حالة خاصة، نعرض القائمة الرئيسية
        await show_main_menu(update, context, new_message=True)

    except Exception as e:
        logger.error(f"Error handling message: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # محاولة تحديد اللغة للرسالة
        try:
            if 'lang' not in locals():
                lang = 'ar'  # افتراضي
        except:
            lang = 'ar'

        error_text = "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else "Sorry, an error occurred. Please try again"

        try:
            await update.message.reply_text(error_text)
        except Exception as reply_error:
            logger.error(f"فشل في إرسال رسالة الخطأ: {str(reply_error)}")
            pass

async def _handle_api_setup(update: Update, context: CallbackContext, user_id: str, message_text: str, state_data: dict, lang: str):
    """معالجة إعداد مفاتيح API"""
    api_setup_state = state_data['api_setup_state']

    if api_setup_state == 'binance_key':
        # حفظ مفتاح Binance API
        await api_manager.save_api_key(user_id, 'binance', message_text)

        # حذف رسالة المستخدم التي تحتوي على مفتاح API
        try:
            await update.message.delete()
            logger.info(f"تم حذف رسالة مفتاح Binance API للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في حذف رسالة مفتاح Binance API: {str(e)}")

        # طلب السر
        await update.message.reply_text(
            "تم حفظ مفتاح API بنجاح. الآن، أدخل سر API (API Secret):" if lang == 'ar' else
            "API Key saved successfully. Now, enter your API Secret:"
        )

        # تحديث الحالة
        user_states[user_id] = {'api_setup_state': 'binance_secret'}

    elif api_setup_state == 'binance_secret':
        # الحصول على المفتاح المخزن مسبقاً
        api_key, _ = await api_manager.get_api_keys(user_id, 'binance')

        # حذف رسالة المستخدم التي تحتوي على سر API
        try:
            await update.message.delete()
            logger.info(f"تم حذف رسالة سر Binance API للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في حذف رسالة سر Binance API: {str(e)}")

        if api_key:
            # حفظ سر Binance API
            await api_manager.save_api_key(user_id, 'binance', api_key, message_text)

            # التحقق من صحة المفاتيح
            is_valid, error_message = await verify_binance_api(api_key, message_text)

            if is_valid:
                await update.message.reply_text(
                    "✅ تم إعداد Binance API بنجاح! يمكنك الآن استخدام البوت بكامل إمكانياته." if lang == 'ar' else
                    "✅ Binance API setup successful! You can now use the bot with full capabilities."
                )
            else:
                await update.message.reply_text(
                    f"❌ مفاتيح API غير صالحة. {error_message if error_message else 'يرجى التحقق والمحاولة مرة أخرى.'}" if lang == 'ar' else
                    f"❌ API keys are invalid. {error_message if error_message else 'Please check and try again.'}"
                )
        else:
            await update.message.reply_text(
                "❌ حدث خطأ في استرجاع مفتاح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else
                "❌ Error retrieving API key. Please try again."
            )

        # إعادة تعيين الحالة
        del user_states[user_id]

    elif api_setup_state in ['gemini_key', 'kucoin_key', 'coinbase_key', 'bybit_key', 'okx_key', 'kraken_key']:
        # استخراج نوع المنصة من حالة الإعداد
        platform = api_setup_state.replace('_key', '')

        # حفظ مفتاح API
        await api_manager.save_api_key(user_id, platform, message_text)

        # حذف رسالة المستخدم التي تحتوي على مفتاح API
        try:
            await update.message.delete()
            logger.info(f"تم حذف رسالة مفتاح {platform} API للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في حذف رسالة مفتاح {platform} API: {str(e)}")

        # التعامل مع المنصات التي تتطلب سر API
        if platform in ['kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
            # طلب السر
            await update.message.reply_text(
                f"تم حفظ مفتاح {platform.capitalize()} API بنجاح. الآن، أدخل سر API (API Secret):" if lang == 'ar' else
                f"{platform.capitalize()} API Key saved successfully. Now, enter your API Secret:"
            )

            # تحديث الحالة
            user_states[user_id] = {'api_setup_state': f'{platform}_secret'}
            return

        # للمنصات التي لا تتطلب سر API (مثل Gemini)
        if platform == 'gemini':
            # التحقق من صحة المفتاح
            is_valid, error_message = await verify_gemini_api(message_text)

            if is_valid:
                await update.message.reply_text(
                    "✅ تم إعداد Gemini API بنجاح! يمكنك الآن استخدام ميزات التحليل المتقدم." if lang == 'ar' else
                    "✅ Gemini API setup successful! You can now use advanced analysis features."
                )
            else:
                await update.message.reply_text(
                    f"❌ مفتاح API غير صالح. {error_message if error_message else 'يرجى التحقق والمحاولة مرة أخرى.'}" if lang == 'ar' else
                    f"❌ API key is invalid. {error_message if error_message else 'Please check and try again.'}"
                )
        else:
            # للمنصات الأخرى التي لا تتطلب تحقق خاص
            await update.message.reply_text(
                f"✅ تم إعداد {platform.capitalize()} API بنجاح!" if lang == 'ar' else
                f"✅ {platform.capitalize()} API setup successful!"
            )

        # إعادة تعيين الحالة
        del user_states[user_id]

    elif api_setup_state in ['kucoin_secret', 'coinbase_secret', 'bybit_secret', 'okx_secret', 'kraken_secret']:
        # استخراج نوع المنصة من حالة الإعداد
        platform = api_setup_state.replace('_secret', '')

        # الحصول على المفتاح المخزن مسبقاً
        api_key, _ = await api_manager.get_api_keys(user_id, platform)

        # حذف رسالة المستخدم التي تحتوي على سر API
        try:
            await update.message.delete()
            logger.info(f"تم حذف رسالة سر {platform} API للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في حذف رسالة سر {platform} API: {str(e)}")

        if api_key:
            # حفظ سر API
            await api_manager.save_api_key(user_id, platform, api_key, message_text)

            # رسالة نجاح
            await update.message.reply_text(
                f"✅ تم إعداد {platform.capitalize()} API بنجاح!" if lang == 'ar' else
                f"✅ {platform.capitalize()} API setup successful!"
            )
        else:
            await update.message.reply_text(
                "❌ حدث خطأ في استرجاع مفتاح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else
                "❌ Error retrieving API key. Please try again."
            )

        # إعادة تعيين الحالة
        del user_states[user_id]

async def _handle_enhanced_symbol_analysis(update: Update, context: CallbackContext, user_id: str, message_text: str, settings: dict, lang: str):
    """معالجة تحليل الرمز المحسن"""
    # تنظيف رمز العملة
    symbol = message_text.upper().replace('/', '')

    # التحقق من صحة رمز العملة
    if len(symbol) < 3 or len(symbol) > 20:
        await update.message.reply_text(
            "رمز العملة غير صحيح. يرجى إدخال رمز صحيح مثل BTCUSDT" if lang == 'ar' else
            "Invalid symbol. Please enter a valid symbol like BTCUSDT",
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(get_text('back_to_main', lang), callback_data='enhanced_analysis_menu')
            ]])
        )
        return

    # قراءة الإعدادات الحديثة مباشرة من قاعدة البيانات لضمان الحصول على القيم المحدثة
    try:
        fresh_settings = subscription_system.get_user_settings(user_id)

        # التحقق من حالة الاشتراك أولاً لتحديد نوع التحليل المناسب
        is_subscribed = subscription_system.is_subscribed_sync(user_id)

        if is_subscribed:
            # للمشتركين: استخدام إعدادات المستخدم أو القيمة الافتراضية 'ai'
            analysis_type = fresh_settings.get('analysis_type', 'ai') if fresh_settings else 'ai'
        else:
            # لغير المشتركين: استخدام التحليل التقليدي دائماً
            analysis_type = 'traditional'

        logger.info(f"🔍 إعدادات المستخدم {user_id} المحدثة من قاعدة البيانات: analysis_type={analysis_type} (مشترك: {is_subscribed})")
    except Exception as e:
        logger.error(f"خطأ في قراءة إعدادات المستخدم {user_id}: {str(e)}")
        # استخدام الإعدادات المرسلة كبديل مع التحقق من الاشتراك
        try:
            is_subscribed = subscription_system.is_subscribed_sync(user_id)
            if is_subscribed:
                analysis_type = settings.get('analysis_type', 'ai')
            else:
                analysis_type = 'traditional'
        except:
            analysis_type = 'traditional'  # افتراضي آمن
        logger.info(f"🔍 استخدام الإعدادات المرسلة للمستخدم {user_id}: analysis_type={analysis_type}")

    # إذا اختار المستخدم التحليل التقليدي، استخدم analyze_symbol بدلاً من التحليل المحسن
    if analysis_type == 'traditional':
        logger.info(f"🔄 المستخدم {user_id} اختار التحليل التقليدي - تحويل إلى analyze_symbol")
        await analyze_symbol(update, context, symbol, analysis_type='traditional')
        return
    elif analysis_type == 'ai':
        logger.info(f"🤖 المستخدم {user_id} اختار تحليل الذكاء الاصطناعي - تحويل إلى analyze_symbol")
        await analyze_symbol(update, context, symbol, analysis_type='ai')
        return
    else:
        logger.info(f"🚀 المستخدم {user_id} اختار التحليل المحسن - نوع التحليل: {analysis_type}")

    # تشغيل التحليل المحسن
    try:
        if enhanced_analyzer is None:
            await update.message.reply_text(
                "النظام المحسن غير متاح حالياً" if lang == 'ar' else
                "Enhanced system not available",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(get_text('back_to_main', lang), callback_data='enhanced_analysis_menu')
                ]])
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.message.reply_text(
            "🚀 جاري التحليل المحسن..." if lang == 'ar' else "🚀 Running enhanced analysis..."
        )

        # النظام الموحد الجديد لا يحتاج نمط تداول محدد
        # يجمع جميع الأنماط الأربعة تلقائياً في تحليل واحد شامل

        # تشغيل التحليل المحسن الموحد
        enhanced_result = await enhanced_analyzer.analyze_crypto_enhanced(symbol, user_id, None, lang)

        if enhanced_result and enhanced_result.get('success', False):
            # الحصول على بيانات السوق لإنشاء الرسم البياني
            market_data = await ca.get_market_data(symbol, user_id=user_id, lang=lang)

            # إرسال نتائج التحليل المحسن
            analysis_text = enhanced_result.get('summary', 'تحليل محسن غير متاح')

            # تنظيف تنسيق Markdown لتجنب أخطاء التحليل
            def clean_markdown_text(text):
                """تنظيف النص من أخطاء تنسيق Markdown"""
                if not text:
                    return text

                import re

                # إصلاح النص العريض غير المغلق
                text = re.sub(r'\*\*([^*\n]+)(?!\*\*)', r'**\1**', text)

                # إصلاح النص المائل غير المغلق
                text = re.sub(r'(?<!\*)\*([^*\n]+)(?!\*)', r'*\1*', text)

                # إصلاح الأقواس المربعة غير المغلقة
                text = re.sub(r'\[([^\]\n]+)(?!\])', r'[\1]', text)

                # إزالة الروابط المكسورة
                text = re.sub(r'\[([^\]]+)\]\([^)]*(?!\))', r'\1', text)

                # إزالة الرموز الخاصة التي قد تسبب مشاكل
                text = text.replace('`', "'")
                text = text.replace('~', '-')

                # تنظيف الأسطر الفارغة المتكررة
                text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)

                # تنظيف المسافات الزائدة
                text = re.sub(r'[ \t]+', ' ', text)
                text = text.strip()

                return text

            analysis_text = clean_markdown_text(analysis_text)

            # تطبيق الحل المبهر لتنسيق النص
            try:
                from utils.utils import fix_bold_formatting
                analysis_text = fix_bold_formatting(analysis_text, lang)
            except ImportError:
                logger.warning("لم يتم العثور على دالة fix_bold_formatting")

            # استخدام النص المنظف مباشرة بدون إضافة معلومات مكررة
            final_text = analysis_text

            # إنشاء أزرار التفاعل
            keyboard = [
                [InlineKeyboardButton(
                    "🔄 تحديث التحليل" if lang == 'ar' else "🔄 Refresh Analysis",
                    callback_data=f'refresh_analysis_{symbol}'
                )],
                [InlineKeyboardButton(
                    "📊 مقارنة أنماط التداول" if lang == 'ar' else "📊 Compare Trading Styles",
                    callback_data=f'compare_styles_{symbol}'
                )],
                [InlineKeyboardButton(
                    "🚀 النظام المحسن" if lang == 'ar' else "🚀 Enhanced System",
                    callback_data='enhanced_analysis_menu'
                )],
                [InlineKeyboardButton(
                    "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                    callback_data='back_to_main'
                )]
            ]

            # محاولة إرسال الرسم البياني مع التحليل
            if market_data and 'chart_data' in market_data and market_data['chart_data']:
                try:
                    # حذف رسالة الانتظار
                    await wait_message.delete()

                    # إرسال الرسم البياني مع التحليل
                    photo_data = market_data['chart_data']
                    logger.info(f"إرسال الرسم البياني مع التحليل المحسن (حجم البيانات: {len(photo_data)} بايت)")

                    # تطبيق الحل المبهر مع معالجة أخطاء متدرجة
                    try:
                        sent_message = await update.message.reply_photo(
                            photo=photo_data,
                            caption=final_text,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN,
                            disable_web_page_preview=True
                        )
                    except Exception as markdown_error:
                        logger.warning(f"فشل إرسال الصورة مع Markdown: {markdown_error}")
                        # محاولة إرسال بدون تنسيق Markdown
                        try:
                            clean_text = final_text.replace('**', '').replace('*', '').replace('`', "'")
                            sent_message = await update.message.reply_photo(
                                photo=photo_data,
                                caption=clean_text,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                disable_web_page_preview=True
                            )
                        except Exception as plain_error:
                            logger.error(f"فشل إرسال الصورة حتى بدون Markdown: {plain_error}")
                            # إرسال رسالة نصية بسيطة
                            await wait_message.edit_text(
                                "✅ تم إكمال التحليل المحسن بنجاح، لكن حدث خطأ في عرض الرسم البياني.",
                                reply_markup=InlineKeyboardMarkup(keyboard)
                            )
                            return
                    logger.info(f"تم إرسال التحليل المحسن مع الرسم البياني بنجاح: message_id={sent_message.message_id}")

                except Exception as chart_error:
                    logger.error(f"خطأ في إرسال الرسم البياني مع التحليل المحسن: {str(chart_error)}")
                    # إرسال التحليل بدون رسم بياني - تطبيق الحل المبهر
                    try:
                        await wait_message.edit_text(
                            final_text,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            parse_mode=ParseMode.MARKDOWN,
                            disable_web_page_preview=True
                        )
                    except Exception as markdown_error:
                        logger.warning(f"فشل إرسال النص مع Markdown: {markdown_error}")
                        # محاولة إرسال بدون تنسيق
                        try:
                            clean_text = final_text.replace('**', '').replace('*', '').replace('`', "'")
                            await wait_message.edit_text(
                                clean_text,
                                reply_markup=InlineKeyboardMarkup(keyboard),
                                disable_web_page_preview=True
                            )
                        except Exception as plain_error:
                            logger.error(f"فشل إرسال النص حتى بدون Markdown: {plain_error}")
                            await wait_message.edit_text(
                                "✅ تم إكمال التحليل المحسن بنجاح، لكن حدث خطأ في عرض النتائج.",
                                reply_markup=InlineKeyboardMarkup(keyboard)
                            )
            else:
                # إرسال التحليل بدون رسم بياني - تطبيق الحل المبهر
                try:
                    await wait_message.edit_text(
                        final_text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode=ParseMode.MARKDOWN,
                        disable_web_page_preview=True
                    )
                except Exception as markdown_error:
                    logger.warning(f"فشل إرسال الرسالة بتنسيق Markdown: {str(markdown_error)}")
                    # محاولة إرسال بدون تنسيق Markdown
                    try:
                        # إزالة علامات التنسيق
                        clean_text = final_text.replace('**', '').replace('*', '').replace('`', "'")
                        await wait_message.edit_text(
                            clean_text,
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
                    except Exception as plain_error:
                        logger.error(f"فشل إرسال الرسالة حتى بدون تنسيق: {str(plain_error)}")
                        # إرسال رسالة خطأ بسيطة
                        await wait_message.edit_text(
                            "✅ تم إكمال التحليل المحسن بنجاح، لكن حدث خطأ في عرض النتائج. يرجى المحاولة مرة أخرى.",
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
        else:
            await wait_message.edit_text(
                f"❌ فشل التحليل المحسن لـ {symbol}: {enhanced_result.get('error', 'خطأ غير معروف')}" if lang == 'ar' else
                f"❌ Enhanced analysis failed for {symbol}: {enhanced_result.get('error', 'Unknown error')}"
            )

    except Exception as e:
        logger.error(f"خطأ في التحليل المحسن: {str(e)}")
        await update.message.reply_text(
            f"❌ حدث خطأ في التحليل المحسن: {str(e)}" if lang == 'ar' else
            f"❌ Enhanced analysis error: {str(e)}"
        )

    # مسح حالة المستخدم
    if user_id in user_states:
        del user_states[user_id]

async def handle_trading_education_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """يعالج استعلامات الأزرار من وحدة تعليم التداول."""
    query = update.callback_query
    await query.answer() # مهم لإعلام تيليجرام بأن الزر تم التعامل معه
    user_id = str(query.from_user.id)
    data = query.data
    logger.info(f"Received trading education callback: {data} from user {user_id}")

    # استيراد user_education_state في بداية الدالة لتجنب مشاكل النطاق
    from education.trading_education import user_education_state

    # استرجاع لغة المستخدم
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    if data.startswith('next_chapter_'):
        try:
            chapter_number = int(data.split('_')[-1])

            # التحقق من النقرات السريعة المتتالية أولاً
            from education.trading_education import is_rapid_click, is_operation_ongoing
            if is_rapid_click(user_id, min_interval=3.0):
                await query.answer(
                    get_text('rapid_click_warning', lang, default="⚠️ يرجى الانتظار قليلاً بين النقرات."),
                    show_alert=True
                )
                return

            # التحقق من وجود عملية توليد فصل جارية
            if is_operation_ongoing(user_id, 'chapter_generation'):
                warning_message = get_text('operation_in_progress', lang, default="⏳ جاري إنشاء الفصل... يرجى الانتظار.")
                warning_message += "\n\n" + get_text('avoid_multiple_clicks', lang, default="⚠️ يرجى عدم النقر على الزر أكثر من مرة لتجنب إنشاء فصول مكررة.")
                await query.answer(warning_message, show_alert=True)
                return

            # إخفاء زر "الفصل التالي" بعد النقر عليه لمنع النقرات المتعددة
            try:
                await query.edit_message_reply_markup(reply_markup=None)
            except Exception as e:
                logger.debug(f"Could not remove next chapter button for user {user_id}: {e}")

            # إخفاء جميع الرسائل السابقة التي تحتوي على أزرار "الفصل التالي"
            state = user_education_state.get(user_id)
            if state:
                logger.info(f"Attempting to delete previous messages for user {user_id}")

                # حذف رسالة المدرس السابقة (التي تحتوي على النص والأزرار)
                if 'last_tutor_message_id' in state and 'last_tutor_chat_id' in state:
                    try:
                        logger.info(f"Deleting tutor message {state['last_tutor_message_id']} for user {user_id}")
                        await context.bot.delete_message(
                            chat_id=state['last_tutor_chat_id'],
                            message_id=state['last_tutor_message_id']
                        )
                        logger.info(f"Successfully deleted tutor message {state['last_tutor_message_id']} for user {user_id}")
                        # إزالة معرف الرسالة من الحالة بعد حذفها
                        del state['last_tutor_message_id']
                        del state['last_tutor_chat_id']
                    except Exception as e:
                        logger.error(f"Could not delete tutor message {state.get('last_tutor_message_id')} for user {user_id}: {e}")

                # حذف رسالة المحادثة مع الذكاء الاصطناعي السابقة (إن وجدت)
                if 'last_ai_conversation_message_id' in state and 'last_ai_conversation_chat_id' in state:
                    try:
                        logger.info(f"Deleting AI conversation message {state['last_ai_conversation_message_id']} for user {user_id}")
                        await context.bot.delete_message(
                            chat_id=state['last_ai_conversation_chat_id'],
                            message_id=state['last_ai_conversation_message_id']
                        )
                        logger.info(f"Successfully deleted AI conversation message {state['last_ai_conversation_message_id']} for user {user_id}")
                        # إزالة معرف الرسالة من الحالة بعد حذفها
                        del state['last_ai_conversation_message_id']
                        del state['last_ai_conversation_chat_id']
                    except Exception as e:
                        logger.error(f"Could not delete AI conversation message {state.get('last_ai_conversation_message_id')} for user {user_id}: {e}")
            else:
                logger.warning(f"No state found for user {user_id} when trying to delete messages")

            # نحتاج إلى تعيين حالة 'is_asking_ai' إلى False عند التنقل
            if user_id in user_education_state:
                user_education_state[user_id]['is_asking_ai'] = False

            await generate_and_send_chapter(update, context, user_id, chapter_number, lang)
        except (ValueError, IndexError) as e:
            logger.error(f"خطأ في تحليل بيانات زر الفصل التالي: {data} - {e}")
            try:
                await query.edit_message_text("حدث خطأ ما. يرجى المحاولة مرة أخرى.")
            except Exception as edit_error:
                 logger.error(f"Failed to edit message after chapter error: {edit_error}")
    elif data == 'start_quiz':
         if user_id in user_education_state:
             user_education_state[user_id]['is_asking_ai'] = False
         await start_quiz(update, context, user_id, lang)
    elif data == 'ask_ai_tutor':
        # تعيين الحالة للسماح بمعالج الرسائل بالتقاط السؤال التالي
        if user_id in user_education_state:
            user_education_state[user_id]['is_asking_ai'] = True
            logger.info(f"User {user_id} set to 'is_asking_ai' state.")
        else:
             # قد يحتاج المستخدم إلى بدء الدورة أولاً
             logger.warning(f"User {user_id} tried to ask AI without education state.")
             try:
                 # التأكد من أن query.message ليس None قبل محاولة التعديل
                 if query.message:
                     await query.edit_message_text("يرجى بدء دورة تعلم التداول أولاً باستخدام /learn_trading_ai")
                 else:
                     # إرسال رسالة جديدة إذا لم يكن هناك رسالة لتعديلها
                     await context.bot.send_message(chat_id=query.effective_chat.id, text="يرجى بدء دورة تعلم التداول أولاً باستخدام /learn_trading_ai")
             except Exception as edit_error:
                 logger.error(f"Failed to edit or send message for ask AI without state: {edit_error}")
             return
        await handle_ask_ai_tutor_button(update, context, user_id, lang)
    elif data == 'learn_trading_ai': # معالجة زر تعلم التداول بالذكاء الاصطناعي
        # التحقق من وجود مفتاح Gemini API
        from analysis.gemini_analysis import get_user_api_client
        model = await get_user_api_client(user_id, 'gemini')
        if not model:
            # إذا لم يكن هناك مفتاح API، توجيه المستخدم لإضافة المفتاح
            await query.answer(
                "يرجى إضافة مفتاح Gemini API أولاً للاستفادة من ميزة التعلم بالذكاء الاصطناعي" if lang == 'ar' else
                "Please add your Gemini API key first to use the AI learning feature",
                show_alert=True
            )
            # توجيه المستخدم لإعداد المفتاح
            await api_setup_command(update, context, preselect_platform='gemini')
            return

        # إذا كان هناك مفتاح API، متابعة العملية
        await handle_learn_trading_ai(update, context)
    elif data == 'add_gemini_key':
         # توجيه المستخدم لإعداد المفتاح
         # التأكد من أن query.message ليس None
         target_update = query if query.message else update # استخدام update إذا لم يكن هناك query.message
         await api_setup_command(target_update, context, preselect_platform='gemini')
         # يمكنك اختياريًا إزالة الرسالة الأصلية أو تعديلها
         try:
             if query.message:
                 await query.edit_message_text("يرجى اتباع التعليمات لإضافة مفتاح Gemini API الخاص بك.")
         except Exception as edit_error:
             logger.error(f"Failed to edit message for add_gemini_key: {edit_error}")
    elif data == 'supplementary_chapters':
        # معالجة زر الفصول التكميلية
        logger.info(f"Handling supplementary_chapters button for user {user_id}")
        await show_supplementary_chapters(update, context, user_id, lang)
    elif data.startswith('supplementary_chapter_'):
        # معالجة اختيار فصل تكميلي محدد
        chapter_id = data.replace('supplementary_chapter_', '')
        logger.info(f"Handling supplementary_chapter_{chapter_id} button for user {user_id}")
        await generate_and_send_supplementary_chapter(update, context, user_id, chapter_id, lang)
    elif data == 'back_to_quiz_results':
        # العودة إلى نتائج الاختبار
        logger.info(f"Handling back_to_quiz_results button for user {user_id}")
        await show_quiz_results_or_next_steps(update, context, user_id, lang)
    else:
        logger.warning(f"تم تلقي استعلام غير معروف لوحدة التعليم: {data}")

async def handle_ai_tutor_message_wrapper(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """غلاف لمعالج رسائل مدرس الذكاء الاصطناعي للتحقق من الحالة."""
    user_id = str(update.effective_user.id)
    message_text = update.message.text

    # استيراد user_education_state لتجنب مشاكل النطاق
    from education.trading_education import user_education_state

    # تحقق مما إذا كان المستخدم في حالة "طرح سؤال"
    # يجب تحسين هذا لاحقًا باستخدام Firestore أو context.user_data
    if user_id in user_education_state and user_education_state[user_id].get('is_asking_ai'):
        logger.info(f"User {user_id} is in 'is_asking_ai' state, handling message: {message_text}")
        await handle_message_for_ai_tutor(update, context)
        # اختياري: إعادة تعيين الحالة بعد الإجابة للسماح بمتابعة الدروس
        # user_education_state[user_id]['is_asking_ai'] = False
        # logger.info(f"Reset 'is_asking_ai' state for user {user_id} after handling message.")
    else:
        # إذا لم يكن المستخدم في حالة طرح سؤال، مرر الرسالة للمعالج العام
        logger.debug(f"User {user_id} not in 'is_asking_ai' state, passing message to general handler: {message_text}")
        await handle_message(update, context) # استدعاء المعالج العام للرسائل

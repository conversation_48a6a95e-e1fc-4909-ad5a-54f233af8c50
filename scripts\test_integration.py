#!/usr/bin/env python3
"""
Integration Test Script
======================

Test the integration of analysis types with subscription system

Author: Augment Agent
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntegrationTester:
    """Integration testing class"""
    
    def __init__(self):
        self.test_symbols = ['BTC', 'ETH', 'BNB']
        
    async def test_enhanced_market_data(self):
        """Test enhanced market data system"""
        logger.info("Testing enhanced market data system...")
        
        try:
            # Import the enhanced market data
            from trading.enhanced_market_data import EnhancedMarketData
            
            # Create instance
            market_data = EnhancedMarketData()
            
            results = {}
            analysis_types = ['basic', 'enhanced', 'ai']
            
            for analysis_type in analysis_types:
                type_results = {}
                
                for symbol in self.test_symbols:
                    try:
                        # Test getting market data
                        data = await market_data.get_market_data(
                            symbol, '1d', 'test_user', analysis_type
                        )
                        
                        if data:
                            type_results[symbol] = {
                                'success': True,
                                'source': data.get('source', 'unknown'),
                                'has_price': 'price' in data,
                                'has_volume': 'volume' in data
                            }
                        else:
                            type_results[symbol] = {'success': False, 'reason': 'no_data'}
                            
                    except Exception as e:
                        type_results[symbol] = {'success': False, 'error': str(e)}
                
                results[analysis_type] = type_results
                logger.info(f"Completed test for {analysis_type}")
            
            return results
            
        except ImportError as e:
            logger.error(f"Import error: {str(e)}")
            return {'error': 'Failed to import enhanced market data'}
        except Exception as e:
            logger.error(f"Test error: {str(e)}")
            return {'error': str(e)}
    
    async def test_market_analyzer(self):
        """Test market analyzer with analysis types"""
        logger.info("Testing market analyzer...")
        
        try:
            # Import market analyzer
            from trading.market_analyzer import MarketAnalyzer
            
            # Create instance
            analyzer = MarketAnalyzer()
            
            results = {}
            analysis_types = ['basic', 'enhanced', 'ai']
            
            for analysis_type in analysis_types:
                try:
                    # Test analysis
                    analysis = await analyzer.analyze_symbol(
                        'BTC', '1d', user_id='test_user', analysis_type=analysis_type
                    )
                    
                    if analysis:
                        results[analysis_type] = {
                            'success': True,
                            'has_signal': 'signal' in analysis,
                            'has_confidence': 'confidence' in analysis,
                            'analysis_type': analysis.get('analysis_type', 'unknown')
                        }
                    else:
                        results[analysis_type] = {'success': False, 'reason': 'no_analysis'}
                        
                except Exception as e:
                    results[analysis_type] = {'success': False, 'error': str(e)}
            
            return results
            
        except ImportError as e:
            logger.error(f"Import error: {str(e)}")
            return {'error': 'Failed to import market analyzer'}
        except Exception as e:
            logger.error(f"Test error: {str(e)}")
            return {'error': str(e)}
    
    async def test_api_connections(self):
        """Test API connections"""
        logger.info("Testing API connections...")
        
        try:
            import aiohttp
            
            results = {}
            
            # Test CoinGecko API
            try:
                async with aiohttp.ClientSession() as session:
                    url = "https://api.coingecko.com/api/v3/ping"
                    async with session.get(url) as response:
                        if response.status == 200:
                            results['coingecko'] = {'success': True, 'status': response.status}
                        else:
                            results['coingecko'] = {'success': False, 'status': response.status}
            except Exception as e:
                results['coingecko'] = {'success': False, 'error': str(e)}
            
            # Test Binance API
            try:
                async with aiohttp.ClientSession() as session:
                    url = "https://api.binance.com/api/v3/ping"
                    async with session.get(url) as response:
                        if response.status == 200:
                            results['binance'] = {'success': True, 'status': response.status}
                        else:
                            results['binance'] = {'success': False, 'status': response.status}
            except Exception as e:
                results['binance'] = {'success': False, 'error': str(e)}
            
            return results
            
        except ImportError as e:
            logger.error(f"Import error: {str(e)}")
            return {'error': 'Failed to import aiohttp'}
        except Exception as e:
            logger.error(f"Test error: {str(e)}")
            return {'error': str(e)}
    
    def print_results(self, test_name: str, results: dict):
        """Print test results in organized format"""
        logger.info(f"\n{'='*50}")
        logger.info(f"Results for {test_name}")
        logger.info(f"{'='*50}")
        
        if 'error' in results:
            logger.error(f"General error: {results['error']}")
            return
        
        for key, value in results.items():
            logger.info(f"\n{key}:")
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    logger.info(f"   {sub_key}: {sub_value}")
            else:
                logger.info(f"   {value}")

async def main():
    """Main function"""
    logger.info("Starting integration tests...")
    
    tester = IntegrationTester()
    
    # List of tests
    tests = [
        ("Enhanced Market Data", tester.test_enhanced_market_data),
        ("Market Analyzer", tester.test_market_analyzer),
        ("API Connections", tester.test_api_connections)
    ]
    
    all_results = {}
    success_count = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\nRunning test: {test_name}")
            results = await test_func()
            
            all_results[test_name] = results
            tester.print_results(test_name, results)
            
            if 'error' not in results:
                success_count += 1
                logger.info(f"✅ {test_name} passed")
            else:
                logger.error(f"❌ {test_name} failed")
                
        except Exception as e:
            logger.error(f"Error running {test_name}: {str(e)}")
            all_results[test_name] = {'error': str(e)}
    
    # Final summary
    logger.info(f"\n{'='*60}")
    logger.info("FINAL RESULTS SUMMARY")
    logger.info(f"{'='*60}")
    logger.info(f"Tests passed: {success_count}/{len(tests)}")
    logger.info(f"Tests failed: {len(tests) - success_count}/{len(tests)}")
    
    if success_count == len(tests):
        logger.info("🎉 All tests passed! System is working correctly.")
        return True
    else:
        logger.warning("⚠️ Some tests failed. System needs review.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

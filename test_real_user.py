#!/usr/bin/env python3
"""
اختبار نظام التداول مع مستخدم حقيقي
"""

import asyncio
import sys
import os

# إضافة مسار src للاستيراد
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_with_real_user():
    """اختبار نظام التداول مع مستخدم حقيقي"""
    
    print("Testing trading system with real user...")
    print("=" * 60)
    
    try:
        # تهيئة Firebase
        from integrations.firebase_init import initialize_firebase
        db = initialize_firebase()
        print("SUCCESS: Firebase initialized")
        
        # البحث عن مستخدمين موجودين
        print("\nSearching for existing users...")
        users_ref = db.collection('users')
        users = users_ref.limit(5).stream()
        
        user_ids = []
        for user in users:
            user_data = user.to_dict()
            user_id = user.id
            user_ids.append(user_id)
            print(f"Found user: {user_id}")
        
        if not user_ids:
            print("No users found in database. Creating a test user...")
            # إنشاء مستخدم للاختبار
            test_user_id = "test_user_12345"
            user_data = {
                'user_id': test_user_id,
                'username': 'test_user',
                'first_name': 'Test',
                'last_name': 'User',
                'created_at': '2025-08-03',
                'is_active': True
            }
            db.collection('users').document(test_user_id).set(user_data)
            user_ids = [test_user_id]
            print(f"Created test user: {test_user_id}")
        
        # استخدام أول مستخدم للاختبار
        test_user_id = user_ids[0]
        print(f"\nUsing user for testing: {test_user_id}")
        
        # تهيئة free_day_system
        from services.free_day_system import FreeDaySystem
        free_day_system = FreeDaySystem(db)
        print("SUCCESS: free_day_system initialized")
        
        # تهيئة subscription_system
        from services.subscription_system import SubscriptionSystem
        subscription_system = SubscriptionSystem(db, free_day_system, None)
        print("SUCCESS: subscription_system initialized")
        
        # تهيئة TradingHandlers
        from trading.trading_db import TradingDatabaseManager
        from trading.auto_trading_system import AutoTradingSystem
        from handlers.trading_handlers import TradingHandlers
        
        # إنشاء مكونات للاختبار
        trading_db_manager = TradingDatabaseManager(db)
        auto_trading_system = AutoTradingSystem(trading_db_manager, None, None, None, None)
        trading_handlers = TradingHandlers(trading_db_manager, auto_trading_system, subscription_system)
        
        print("SUCCESS: TradingHandlers initialized")
        
        # اختبار فحص الاشتراك قبل منح اليوم المجاني
        print(f"\nTesting subscription check BEFORE granting free day...")
        result_before = await trading_handlers._check_premium_subscription(test_user_id)
        print(f"Subscription check result BEFORE: {result_before}")
        
        # منح يوم مجاني
        print(f"\nGranting 1 hour free trial to user {test_user_id}...")
        free_day_granted = free_day_system.grant_free_day(test_user_id, duration_hours=1)
        print(f"Free day grant result: {free_day_granted}")
        
        if free_day_granted:
            # اختبار فحص الاشتراك بعد منح اليوم المجاني
            print(f"\nTesting subscription check AFTER granting free day...")
            result_after = await trading_handlers._check_premium_subscription(test_user_id)
            print(f"Subscription check result AFTER: {result_after}")
            
            if result_after:
                print("SUCCESS! User can access auto trading after free day grant")
            else:
                print("FAILED! User cannot access auto trading despite free day grant")
                
            # فحص تفاصيل اليوم المجاني
            print(f"\nChecking free day details...")
            has_free_day = await free_day_system.has_active_free_day(test_user_id)
            print(f"Has active free day: {has_free_day}")
            
            is_free_day_active = await free_day_system.is_free_day_active(test_user_id)
            print(f"Is free day active: {is_free_day_active}")
        else:
            print("FAILED to grant free day!")
        
        print("\nTest completed!")
        
    except Exception as e:
        print(f"ERROR in test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_with_real_user())

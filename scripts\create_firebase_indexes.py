#!/usr/bin/env python3
"""
🔧 سكريبت إنشاء فهارس Firebase تلقائياً
=====================================

سكريبت لإنشاء الفهارس المطلوبة في Firestore لحل مشاكل الاستعلامات
في نظام التداول الآلي.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import os
import sys
import json
import logging
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# إعداد السجل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_trading_recommendations_indexes():
    """إنشاء فهارس trading_recommendations"""
    
    print("🔧 إنشاء فهارس Firebase للتوصيات التجارية")
    print("=" * 60)
    
    # معرف المشروع
    project_id = "tradingtelegram-da632"
    
    # الفهارس المطلوبة لـ trading_recommendations
    indexes_config = {
        "indexes": [
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {"fieldPath": "status", "order": "ASCENDING"},
                    {"fieldPath": "user_id", "order": "ASCENDING"},
                    {"fieldPath": "created_at", "order": "DESCENDING"}
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION", 
                "fields": [
                    {"fieldPath": "user_id", "order": "ASCENDING"},
                    {"fieldPath": "created_at", "order": "DESCENDING"}
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {"fieldPath": "user_id", "order": "ASCENDING"},
                    {"fieldPath": "status", "order": "ASCENDING"},
                    {"fieldPath": "created_at", "order": "DESCENDING"}
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {"fieldPath": "expires_at", "order": "ASCENDING"},
                    {"fieldPath": "status", "order": "ASCENDING"}
                ]
            }
        ]
    }
    
    print("📋 الفهارس المطلوبة:")
    for i, index in enumerate(indexes_config["indexes"], 1):
        fields = " + ".join([f"{field['fieldPath']}({field['order']})" for field in index["fields"]])
        print(f"   {i}. {index['collectionGroup']}: {fields}")
    
    # حفظ ملف التكوين
    config_file = "firestore_indexes.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(indexes_config, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 تم حفظ تكوين الفهارس في: {config_file}")
    
    # روابط إنشاء الفهارس المباشرة
    base_url = f"https://console.firebase.google.com/v1/r/project/{project_id}/firestore/indexes"
    
    # الرابط من رسالة الخطأ الأصلية
    main_index_url = f"{base_url}?create_composite=CmVwcm9qZWN0cy90cmFkaW5ndGVsZWdyYW0tZGE2MzIvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL3RyYWRpbmdfcmVjb21tZW5kYXRpb25zL2luZGV4ZXMvXxABGgoKBnN0YXR1cxABGgsKB3VzZXJfaWQQARoOCgpjcmVhdGVkX2F0EAIaDAoIX19uYW1lX18QAg"
    
    print("\n🔗 روابط إنشاء الفهارس:")
    print(f"   الفهرس الرئيسي (مطلوب): {main_index_url}")
    print(f"   Firebase Console: {base_url}")
    
    print("\n📝 خطوات الإنشاء:")
    print("   1. انقر على الرابط أعلاه")
    print("   2. سجل دخولك إلى Firebase Console")
    print("   3. اضغط 'Create Index'")
    print("   4. انتظر حتى اكتمال إنشاء الفهرس")
    print("   5. أعد تشغيل البوت")
    
    return True

def verify_indexes_creation():
    """التحقق من إنشاء الفهارس"""
    try:
        # محاولة الاتصال بـ Firestore للتحقق
        from google.cloud import firestore
        
        db = firestore.Client()
        
        # اختبار استعلام يتطلب الفهرس
        test_query = db.collection('trading_recommendations').where(
            'status', '==', 'active'
        ).where(
            'user_id', '==', 'test_user'
        ).order_by('created_at', direction=firestore.Query.DESCENDING).limit(1)
        
        # محاولة تنفيذ الاستعلام
        docs = list(test_query.stream())
        
        print("✅ الفهارس تعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        error_str = str(e)
        if "requires an index" in error_str:
            print("❌ الفهارس لم يتم إنشاؤها بعد")
            print(f"   الخطأ: {error_str}")
            return False
        else:
            print(f"⚠️ خطأ في التحقق: {error_str}")
            return None

def main():
    """الدالة الرئيسية"""
    print(f"🚀 بدء إنشاء فهارس Firebase - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # إنشاء الفهارس
        if create_trading_recommendations_indexes():
            print("\n✅ تم إعداد الفهارس بنجاح!")
            
            # التحقق من الفهارس
            print("\n🔍 التحقق من الفهارس...")
            verification_result = verify_indexes_creation()
            
            if verification_result is True:
                print("\n🎉 جميع الفهارس تعمل بشكل صحيح!")
            elif verification_result is False:
                print("\n⏳ يرجى إنشاء الفهارس من الروابط أعلاه")
            else:
                print("\n⚠️ لا يمكن التحقق من الفهارس حالياً")
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء الفهارس: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    main()

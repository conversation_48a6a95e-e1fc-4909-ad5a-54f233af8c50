"""
🛡️ إدارة المخاطر والامتثال الشرعي
=================================

نظام شامل لإدارة المخاطر والتأكد من الامتثال للأحكام الشرعية
في التداول والاستثمار في العملات الرقمية.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RiskLevel(Enum):
    """مستويات المخاطرة"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    EXTREME = "extreme"

class ComplianceStatus(Enum):
    """حالة الامتثال الشرعي"""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    QUESTIONABLE = "questionable"
    UNKNOWN = "unknown"

@dataclass
class RiskAssessment:
    """تقييم المخاطر"""
    symbol: str
    risk_level: RiskLevel
    risk_score: float  # 0.0 - 1.0
    volatility_risk: float
    liquidity_risk: float
    market_cap_risk: float
    technical_risk: float
    compliance_status: ComplianceStatus
    warnings: List[str]
    recommendations: List[str]

class IslamicComplianceFilter:
    """فلتر الامتثال الشرعي للعملات الرقمية"""
    
    def __init__(self):
        """تهيئة فلتر الامتثال الشرعي"""
        
        # العملات المحرمة صراحة
        self.prohibited_symbols = {
            # عملات القمار والمراهنات
            'FUN', 'DOGE', 'SHIB', 'FLOKI', 'SAFEMOON',
            # عملات الربا والفوائد المركبة
            'COMP', 'AAVE', 'YFI', 'CRV', 'UNI',
            # عملات مشبوهة أو احتيالية معروفة
            'SQUID', 'TITAN', 'IRON'
        }
        
        # الكلمات المحرمة في أسماء العملات
        self.prohibited_keywords = {
            'casino', 'bet', 'gambling', 'poker', 'lottery',
            'interest', 'yield', 'lending', 'borrow',
            'adult', 'porn', 'sex', 'wine', 'alcohol',
            'pig', 'pork', 'bacon'
        }
        
        # العملات المقبولة شرعياً
        self.approved_symbols = {
            'BTC', 'ETH', 'BNB', 'ADA', 'DOT', 'LINK',
            'LTC', 'BCH', 'XLM', 'VET', 'ALGO', 'ATOM',
            'SOL', 'AVAX', 'MATIC', 'FTM', 'NEAR', 'ICP'
        }
    
    def check_symbol_compliance(self, symbol: str, name: str = "", description: str = "") -> ComplianceStatus:
        """
        فحص امتثال العملة للأحكام الشرعية
        
        Args:
            symbol: رمز العملة
            name: اسم العملة
            description: وصف العملة
            
        Returns:
            حالة الامتثال الشرعي
        """
        try:
            symbol = symbol.upper()
            
            # فحص القائمة المحرمة
            if symbol in self.prohibited_symbols:
                logger.warning(f"العملة {symbol} في القائمة المحرمة")
                return ComplianceStatus.NON_COMPLIANT
            
            # فحص القائمة المقبولة
            if symbol in self.approved_symbols:
                return ComplianceStatus.COMPLIANT
            
            # فحص الكلمات المحرمة في الاسم والوصف
            text_to_check = f"{name} {description}".lower()
            for keyword in self.prohibited_keywords:
                if keyword in text_to_check:
                    logger.warning(f"العملة {symbol} تحتوي على كلمة محرمة: {keyword}")
                    return ComplianceStatus.NON_COMPLIANT
            
            # فحص أنماط مشبوهة
            if self._has_suspicious_patterns(symbol, name):
                return ComplianceStatus.QUESTIONABLE
            
            # إذا لم يتم العثور على مشاكل واضحة
            return ComplianceStatus.UNKNOWN
            
        except Exception as e:
            logger.error(f"خطأ في فحص امتثال العملة {symbol}: {str(e)}")
            return ComplianceStatus.UNKNOWN
    
    def _has_suspicious_patterns(self, symbol: str, name: str) -> bool:
        """
        فحص الأنماط المشبوهة في العملة
        
        Args:
            symbol: رمز العملة
            name: اسم العملة
            
        Returns:
            True إذا كانت العملة مشبوهة
        """
        # عملات الميم المشبوهة
        meme_patterns = ['DOGE', 'SHIB', 'FLOKI', 'ELON', 'MOON', 'SAFE']
        if any(pattern in symbol for pattern in meme_patterns):
            return True
        
        # أسماء مشبوهة
        suspicious_names = ['meme', 'joke', 'fun', 'moon', 'rocket', 'lambo']
        name_lower = name.lower()
        if any(pattern in name_lower for pattern in suspicious_names):
            return True
        
        return False

class RiskManager:
    """مدير المخاطر الشامل"""
    
    def __init__(self):
        """تهيئة مدير المخاطر"""
        self.compliance_filter = IslamicComplianceFilter()
        
        # حدود المخاطرة
        self.max_position_size = 0.1  # 10% من رأس المال كحد أقصى
        self.max_daily_loss = 0.05    # 5% خسارة يومية كحد أقصى
        self.min_market_cap = 100_000_000  # 100 مليون دولار كحد أدنى
        self.max_volatility = 0.15    # 15% تقلبات كحد أقصى
    
    def assess_trading_risk(self, symbol: str, market_data: Dict[str, Any], 
                           position_size: float, user_capital: float) -> RiskAssessment:
        """
        تقييم مخاطر التداول
        
        Args:
            symbol: رمز العملة
            market_data: بيانات السوق
            position_size: حجم المركز المقترح
            user_capital: رأس مال المستخدم
            
        Returns:
            تقييم شامل للمخاطر
        """
        try:
            warnings = []
            recommendations = []
            
            # فحص الامتثال الشرعي
            compliance_status = self.compliance_filter.check_symbol_compliance(
                symbol, 
                market_data.get('name', ''),
                market_data.get('description', '')
            )
            
            if compliance_status == ComplianceStatus.NON_COMPLIANT:
                warnings.append("⚠️ هذه العملة غير متوافقة مع الأحكام الشرعية")
                recommendations.append("تجنب التداول في هذه العملة")
            
            # تقييم المخاطر التقنية
            volatility_risk = self._calculate_volatility_risk(market_data)
            liquidity_risk = self._calculate_liquidity_risk(market_data)
            market_cap_risk = self._calculate_market_cap_risk(market_data)
            technical_risk = self._calculate_technical_risk(market_data)
            
            # حساب النتيجة الإجمالية للمخاطر
            risk_score = (volatility_risk + liquidity_risk + market_cap_risk + technical_risk) / 4
            
            # تحديد مستوى المخاطرة
            if risk_score < 0.3:
                risk_level = RiskLevel.LOW
            elif risk_score < 0.6:
                risk_level = RiskLevel.MEDIUM
            elif risk_score < 0.8:
                risk_level = RiskLevel.HIGH
            else:
                risk_level = RiskLevel.EXTREME
            
            # فحص حجم المركز
            position_ratio = position_size / user_capital
            if position_ratio > self.max_position_size:
                warnings.append(f"⚠️ حجم المركز كبير جداً ({position_ratio:.1%})")
                recommendations.append(f"قلل حجم المركز إلى أقل من {self.max_position_size:.1%}")
            
            # توصيات إضافية
            if risk_level == RiskLevel.HIGH:
                recommendations.append("استخدم وقف خسارة ضيق")
                recommendations.append("راقب السوق عن كثب")
            elif risk_level == RiskLevel.EXTREME:
                recommendations.append("تجنب التداول في هذه العملة")
                warnings.append("⚠️ مخاطر عالية جداً")
            
            return RiskAssessment(
                symbol=symbol,
                risk_level=risk_level,
                risk_score=risk_score,
                volatility_risk=volatility_risk,
                liquidity_risk=liquidity_risk,
                market_cap_risk=market_cap_risk,
                technical_risk=technical_risk,
                compliance_status=compliance_status,
                warnings=warnings,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر للعملة {symbol}: {str(e)}")
            return RiskAssessment(
                symbol=symbol,
                risk_level=RiskLevel.EXTREME,
                risk_score=1.0,
                volatility_risk=1.0,
                liquidity_risk=1.0,
                market_cap_risk=1.0,
                technical_risk=1.0,
                compliance_status=ComplianceStatus.UNKNOWN,
                warnings=["خطأ في تقييم المخاطر"],
                recommendations=["تجنب التداول حتى يتم حل المشكلة"]
            )
    
    def _calculate_volatility_risk(self, market_data: Dict[str, Any]) -> float:
        """حساب مخاطر التقلبات"""
        try:
            # استخدام التغيير اليومي كمؤشر للتقلبات
            daily_change = abs(market_data.get('price_change_percentage_24h', 0)) / 100
            
            # تطبيع القيمة (0-1)
            volatility_risk = min(daily_change / self.max_volatility, 1.0)
            return volatility_risk
            
        except Exception:
            return 0.5  # مخاطرة متوسطة في حالة عدم توفر البيانات
    
    def _calculate_liquidity_risk(self, market_data: Dict[str, Any]) -> float:
        """حساب مخاطر السيولة"""
        try:
            volume_24h = market_data.get('total_volume', 0)
            market_cap = market_data.get('market_cap', 1)
            
            # نسبة الحجم إلى القيمة السوقية
            volume_ratio = volume_24h / market_cap if market_cap > 0 else 0
            
            # كلما قلت النسبة، زادت المخاطر
            if volume_ratio > 0.1:
                return 0.1  # سيولة عالية
            elif volume_ratio > 0.05:
                return 0.3  # سيولة متوسطة
            elif volume_ratio > 0.01:
                return 0.6  # سيولة منخفضة
            else:
                return 0.9  # سيولة ضعيفة جداً
                
        except Exception:
            return 0.7  # مخاطرة عالية في حالة عدم توفر البيانات
    
    def _calculate_market_cap_risk(self, market_data: Dict[str, Any]) -> float:
        """حساب مخاطر القيمة السوقية"""
        try:
            market_cap = market_data.get('market_cap', 0)
            
            if market_cap > 10_000_000_000:  # أكثر من 10 مليار
                return 0.1  # مخاطرة منخفضة
            elif market_cap > 1_000_000_000:  # أكثر من مليار
                return 0.3  # مخاطرة متوسطة منخفضة
            elif market_cap > self.min_market_cap:  # أكثر من 100 مليون
                return 0.5  # مخاطرة متوسطة
            elif market_cap > 10_000_000:  # أكثر من 10 مليون
                return 0.7  # مخاطرة عالية
            else:
                return 0.9  # مخاطرة عالية جداً
                
        except Exception:
            return 0.8  # مخاطرة عالية في حالة عدم توفر البيانات
    
    def _calculate_technical_risk(self, market_data: Dict[str, Any]) -> float:
        """حساب المخاطر التقنية"""
        try:
            # فحص عمر العملة
            # العملات الجديدة أكثر خطورة
            # هذا مؤشر بسيط، يمكن تطويره أكثر
            
            # فحص الترتيب في السوق
            market_cap_rank = market_data.get('market_cap_rank', 1000)
            
            if market_cap_rank <= 50:
                return 0.2  # عملات مستقرة
            elif market_cap_rank <= 100:
                return 0.4  # عملات جيدة
            elif market_cap_rank <= 500:
                return 0.6  # عملات متوسطة
            else:
                return 0.8  # عملات عالية المخاطر
                
        except Exception:
            return 0.6  # مخاطرة متوسطة في حالة عدم توفر البيانات
    
    def validate_trading_parameters(self, symbol: str, action: str, amount: float, 
                                  price: float, user_capital: float) -> Tuple[bool, List[str]]:
        """
        التحقق من صحة معاملات التداول
        
        Args:
            symbol: رمز العملة
            action: نوع العملية (buy/sell)
            amount: الكمية
            price: السعر
            user_capital: رأس مال المستخدم
            
        Returns:
            (صالح، قائمة الأخطاء)
        """
        errors = []
        
        try:
            # فحص المعاملات الأساسية
            if amount <= 0:
                errors.append("الكمية يجب أن تكون أكبر من صفر")
            
            if price <= 0:
                errors.append("السعر يجب أن يكون أكبر من صفر")
            
            # فحص حجم المركز
            position_value = amount * price
            position_ratio = position_value / user_capital
            
            if position_ratio > self.max_position_size:
                errors.append(f"حجم المركز كبير جداً ({position_ratio:.1%}). الحد الأقصى {self.max_position_size:.1%}")
            
            # فحص الحد الأدنى للتداول
            if position_value < 10:  # 10 دولار كحد أدنى
                errors.append("قيمة التداول أقل من الحد الأدنى (10 دولار)")
            
            return len(errors) == 0, errors
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من معاملات التداول: {str(e)}")
            return False, ["خطأ في التحقق من المعاملات"]

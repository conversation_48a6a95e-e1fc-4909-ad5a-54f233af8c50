# -*- coding: utf-8 -*-
import logging
import re

logger = logging.getLogger(__name__)

# Dictionary for translations
translations = {
    'ar': {
        # Main menu translations
        'welcome': """🌟 مرحباً بك في بوت التحليل الفني! 🌟

بوت متخصص في تحليل العملات الرقمية باستخدام الذكاء الاصطناعي والمؤشرات الفنية المتقدمة.

📊 **الميزات الرئيسية:**
• التحليل المحسن الموحد (يجمع 4 أنماط تحليل)
• تنبيهات سعرية ذكية
• تعلم التداول مع الذكاء الاصطناعي
• دعم متعدد المنصات (Binance, KuCoin, وغيرها)

تحليلات: 3/3 🔍
تنبيهات: 1/1 🔔
اليوم المجاني القادم: الاثنين (09-06-2025) 🎁

🔑 API: Binance ✅ KuCoin ❌ Coinbase ❌ Bybit ❌ OKX ❌ Kraken ❌

✨ **للحصول على مميزات إضافية (5 USD أسبوعياً):**
• نظام التداول الآلي الذكي 🤖
• تنبيهات سعرية غير محدودة 🔔
• إعدادات وتخصيص متقدم ⚙️
• تحليلات غير محدودة 📈
• جميع المؤشرات الفنية 🎯
• تحليل بالذكاء الاصطناعي Gemini 🤖

اضغط على ✨ ترقية الحساب للاشتراك""",
        'features': "المميزات المتوفرة:",
        'analyze': "📊 التحليل المحسن",
        'active_alerts': "🔔 التنبيهات النشطة",
        'help': "💡 المساعدة",
        'change_language': "🌐 تغيير اللغة",
        'manage_currencies': "💱 إدارة العملات",
        'back_to_main': "🔙 القائمة الرئيسية",

        # Trading education translations
        'welcome_trading_ai': "أهلاً بك في دورة تعلم التداول بالذكاء الاصطناعي! 📈",
        'add_gemini_key_prompt': "للبدء، يرجى إضافة مفتاح Gemini API الخاص بك. هذا سيسمح لي بتوليد محتوى تعليمي مخصص لك.",
        'add_gemini_key_button': "🔑 إضافة مفتاح Gemini API",
        'gemini_access_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن. يرجى التأكد من إضافة مفتاح API صالح.",
        'gemini_tutor_error': "حدث خطأ أثناء محاولة الإجابة على سؤالك. يرجى المحاولة مرة أخرى لاحقًا.",
        'lesson_generation_error': "عذرًا، لا يمكنني الوصول إلى Gemini الآن لتوليد الدرس. يرجى التأكد من إضافة مفتاح API صالح.",
        'quota_exceeded_error': "تم تجاوز حصة الاستخدام المجانية لـ Gemini. يرجى المحاولة مرة أخرى بعد قليل أو ترقية حسابك في Google AI Studio.",
        'chapter_button': "الفصل {number}",
        'next_chapter_button': "الفصل التالي ❯",
        'take_quiz_button': "📝 إجراء الاختبار",
        'ask_tutor_prompt': "يمكنك أيضًا طرح أي سؤال يتعلق بالتداول وسأبذل قصارى جهدي للإجابة عليه باستخدام Gemini.",
        'ask_tutor_button': '❓ اسأل مدرس الذكاء الاصطناعي',
        'ask_tutor_instruction': "يرجى كتابة سؤالك المتعلق بالتداول.",
        'quiz_already_started': "📝 يبدو أنك بدأت الاختبار بالفعل. يرجى إكمال الإجابة على الأسئلة.",
        'course_completed': "لقد أكملت الدورة الأساسية والاختبار! 🎉",
        'error_starting_course': "حدث خطأ ما. يرجى محاولة بدء الدورة مرة أخرى باستخدام /learn_trading_ai",
        'tutor_prompt_header': "أنت مدرس خبير في التداول. المستخدم يدرس حاليًا الفصل {chapter_number} حول موضوع '{chapter_topic}'. أجب على سؤاله التالي بالعربية مع التركيز على المفاهيم المتعلقة بهذا الموضوع قدر الإمكان:\n\nالسؤال: {question}\n\nالإجابة:",
        'ai_conversation_system_instruction': "أنت مساعد تعليمي متخصص في تداول العملات الرقمية. المستخدم يدرس حاليًا الفصل المتعلق بـ '{chapter_topic}'. أجب على أسئلة المستخدم المتعلقة بهذا الموضوع أو بمواضيع التداول بشكل عام بالعربية. حافظ على إجاباتك واضحة وموجزة ومناسبة للمبتدئين. استخدم تنسيق Markdown عند الحاجة.",
        'enhanced_tutor_prompt': "يرجى تقديم إجابة مفصلة ومتكاملة باللغة العربية الفصحى أو السعودية. الإجابة القصيرة جدًا غير مقبولة. يجب أن تتضمن إجابتك:\n1. شرحًا مفصلاً للمفاهيم المتعلقة بالسؤال\n2. أمثلة عملية توضح الفكرة\n3. نصائح وإرشادات للمتداولين المبتدئين\n4. معلومات إضافية مفيدة متعلقة بالموضوع\n\n**مهم جداً: تجنب اللهجة المصرية تماماً. لا تستخدم كلمات مثل (بص، وده، ده، دي، دول، كده، ازاي، ايه). استخدم الفصحى أو اللهجة السعودية فقط.**",
        'enhanced_chapter_prompt': "يرجى تقديم محتوى تعليمي مفصل ومتكامل باللغة العربية الفصحى أو السعودية. المحتوى القصير جدًا غير مقبول. يجب أن يتضمن المحتوى:\n1. شرحًا مفصلاً للمفاهيم الأساسية في هذا الفصل\n2. أمثلة عملية توضح المفاهيم\n3. نصائح وإرشادات للمتداولين المبتدئين\n4. معلومات إضافية مفيدة متعلقة بالموضوع\n5. استخدام الرموز التعبيرية (الإيموجي) بشكل مناسب\n\n**مهم جداً: تجنب اللهجة المصرية تماماً. لا تستخدم كلمات مثل (بص، وده، ده، دي، دول، كده، ازاي، ايه). استخدم الفصحى أو اللهجة السعودية فقط.**",

        # Supplementary Chapters Titles and Descriptions - Arabic
        'basics_chapter_title': "أساسيات التداول - مراجعة شاملة",
        'basics_chapter_desc': "مراجعة شاملة للمفاهيم الأساسية في التداول",
        'risk_management_title': "إدارة المخاطر للمبتدئين",
        'risk_management_desc': "تعلم كيفية حماية رأس مالك وتقليل الخسائر",
        'intermediate_strategies_title': "استراتيجيات التداول المتوسطة",
        'intermediate_strategies_desc': "استراتيجيات متقدمة للمتداولين ذوي الخبرة المتوسطة",
        'technical_analysis_title': "التحليل الفني المتقدم",
        'technical_analysis_desc': "فهم متعمق للمؤشرات والأنماط الفنية",
        'advanced_strategies_title': "استراتيجيات التداول المتقدمة",
        'advanced_strategies_desc': "استراتيجيات متقدمة للمتداولين المحترفين",
        'market_psychology_title': "علم نفس السوق والتداول العاطفي",
        'market_psychology_desc': "فهم العوامل النفسية التي تؤثر على قرارات التداول",
        'indicators_title': "تعمق في المؤشرات الفنية",
        'indicators_desc': "شرح مفصل للمؤشرات الفنية وكيفية استخدامها",
        'general_trading_title': "مفاهيم التداول الأساسية",
        'general_trading_desc': "مراجعة شاملة لمفاهيم التداول الأساسية",
        'chapter_gen_prompt_header': "أنت مدرس تداول خبير ومؤلف محتوى تعليمي بالعربية. مهمتك هي إنشاء محتوى تعليمي للفصل رقم {chapter_number} من دورة تداول للمبتدئين مكونة من 10 فصول.",
        'guide_add_gemini_key': "يرجى استخدام الأمر /add_gemini_key لإضافة مفتاح API الخاص بك.",
        'complete_chapters_first': "يرجى إكمال جميع الفصول قبل إجراء الاختبار.",
        'quiz_starting': "🎯 بدء الاختبار!\n\nسيتم إرسال الأسئلة واحداً تلو الآخر. لديك 30 ثانية للإجابة على كل سؤال.",
        'generating_quiz': "⏳ جاري إنشاء الاختبار...",
        'quiz_results': "نتائج الاختبار",
        'correct_answers': "الإجابات الصحيحة",
        'percentage': "النسبة المئوية",
        'excellent_result': "🌟 ممتاز! لديك فهم ممتاز للمفاهيم.",
        'good_result': "👍 جيد! لديك فهم جيد للمفاهيم الأساسية.",
        'average_result': "🤔 متوسط. هناك بعض المفاهيم التي تحتاج مراجعة.",
        'needs_improvement': "📚 تحتاج إلى مزيد من الدراسة. لا تقلق، استمر في التعلم!",
        'results_error': "حدث خطأ أثناء عرض النتائج. يرجى المحاولة مرة أخرى.",
        'ask_again_prompt': "يمكنك طرح سؤال آخر أو الانتقال إلى الفصل التالي.",
        'operation_in_progress': "⏳ جاري إنشاء الفصل... يرجى الانتظار.",
        'avoid_multiple_clicks': "⚠️ يرجى عدم النقر على الزر أكثر من مرة لتجنب إنشاء فصول مكررة.",
        'rapid_click_warning': "⚠️ يرجى الانتظار قليلاً بين النقرات.",
        'quiz_generation_in_progress': "⏳ جاري إنشاء الاختبار... يرجى الانتظار.",
        'continue_or_ask': "يمكنك الآن المتابعة في الفصل أو طرح سؤال آخر.",
        'review_material_title': "مواد للمراجعة",
        'supplementary_chapters': "📚 فصول تكميلية مخصصة",
        'back_to_main': "🏠 العودة إلى القائمة الرئيسية",
        'next_steps': "ماذا تريد أن تفعل الآن؟",
        'quiz_score': "النتيجة",
        'quiz_performance': "التقييم",
        'generating_chapter': "⏳ جاري إنشاء الفصل التكميلي...",
        'generating_main_chapter': "⏳ جاري إنشاء فصل مخصص لك...",
        'preparing_content': "⏳ يتم تحضير المحتوى التعليمي...",
        'chapter_not_found': "الفصل المطلوب غير موجود.",
        'chapter_generation_error': "حدث خطأ أثناء إنشاء الفصل التكميلي. يرجى المحاولة مرة أخرى لاحقًا.",
        'back_to_chapters': "🔙 العودة إلى قائمة الفصول التكميلية",
        'no_supplementary_chapters': "لا توجد فصول تكميلية متاحة حاليًا. يرجى المحاولة مرة أخرى لاحقًا.",
        'supplementary_chapters_list': "📚 الفصول التكميلية المخصصة لك بناءً على نتيجة الاختبار:",
        'back_button': "🔙 رجوع",
        'review_generation_error': "حدث خطأ أثناء إنشاء مواد المراجعة. يرجى المحاولة مرة أخرى لاحقًا.",
        # الشروط والأحكام
        'terms_and_conditions_title': "📜 الشروط والأحكام",
        'terms_and_conditions_content': "\u26a0 إخلاء المسؤولية القانوني\n\n1. هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية\n2. أنت المسؤول الوحيد عن قراراتك الاستثمارية\n3. لا نتحمل مسؤولية أي خسائر مالية\n4. استخدام البوت يعني موافقتك على الشروط\n5. عدم إعطاء مفاتيح الوصول بدقة قد يؤدي لفقدان اشتراكك\n6. لا يوجد دعم فني - اتبع التعليمات بدقة\n7. المؤشرات الفنية لا يمكن أن تكون 100% دقيقة وتعتمد على بيانات تاريخية\n8. الأسواق المالية متقلبة تنطوي على مخاطر عالية\n9. لا نضمن أي أرباح أو عوائد مالية\n10. البوت قد يتعطل في أي وقت دون إشعار مسبق\n11. نحن بريئون تماما من أي استخدام للبوت في تداول العملات\n12. أي قرارات تداول تتخذها بناء على تحليلات البوت هي مسؤوليتك الكاملة\n13. مطور البوت غير مسؤول عن أي خسائر مالية قد تنتج من استخدام البوت\n14. استخدام البوت للتداول يتم على مسؤوليتك الشخصية الكاملة\n\n🔒 شروط الاستخدام:\n\u2022 البوت للتحليل الفني فقط\n\u2022 لا نضمن دقة التحليلات\n\u2022 لا نقدم استشارات مالية\n\u2022 الاشتراك غير قابل للاسترداد\n\u2022 استخدام البوت لأغراض غير مشروعة\n\u2022 يحظر مشاركة حساب الاشتراك مع الآخرين\n\u2022 نحتفظ بحق إيقاف أي حساب يخالف الشروط\n\u2022 قد نقوم بتحديث الشروط في أي وقت دون إشعار\n\u2022 البيانات المقدمة قد تكون متأخرة عن السوق الفعلي\n\u2022 لا نتحمل مسؤولية أي أعطال فنية أو انقطاع في الخدمة\n\n\u26a0 تحذيرات إضافية:\n\u2022 تداول العملات الرقمية ينطوي على مخاطر عالية\n\u2022 لا تستثمر أكثر مما يمكنك تحمل خسارته\n\u2022 قم بإجراء بحثك الخاص قبل اتخاذ أي قرار\n\u2022 كن حذرا من عمليات الاحتيال والمشاريع الوهمية\n\u2022 تأكد من فهم آلية عمل المؤشرات الفنية قبل استخدامها",
        'terms_agree_button': "✅ أوافق على الشروط والأحكام",
        'terms_decline_button': "❌ لا أوافق",
        'terms_agree_success': "شكراً لموافقتك على الشروط والأحكام",
        'terms_decline_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت",
        'terms_required_message': "للأسف، يجب الموافقة على الشروط والأحكام لاستخدام البوت. يمكنك إعادة تشغيل البوت في أي وقت للموافقة على الشروط. 🔄",
        'language_selected': "تم اختيار اللغة بنجاح",
        'error_saving_settings': "حدث خطأ أثناء حفظ الإعدادات. الرجاء المحاولة مرة أخرى",

        # AI Learning Content Generation Prompts - Arabic
        'chapter_generation_intro': "أنت خبير في تعليم التداول بالعملات الرقمية ومصمم محتوى تعليمي محترف. اشرح الموضوع التالي للمبتدئين بطريقة منظمة وجذابة:",
        'chapter_generation_topic': "**الموضوع:** {topic}",
        'chapter_generation_requirements': "**المطلوب:**",
        'chapter_generation_req1': "1. **هيكلة واضحة ومنظمة:** قسم المحتوى إلى أقسام واضحة مع عناوين فرعية مميزة. ابدأ بمقدمة موجزة، ثم الشرح المفصل، وانته بخلاصة أو نقاط مهمة.",
        'chapter_generation_req2': "2. **شرح مفصل وواضح:** استخدم لغة بسيطة ومباشرة مع تدرج منطقي في المعلومات من البسيط إلى المعقد.",
        'chapter_generation_req3': "3. **أمثلة عملية وتطبيقية:** قدم أمثلة واقعية وحالات عملية لتوضيح المفاهيم مع أرقام ومواقف حقيقية.",
        'chapter_generation_req4': "4. **تنسيق Telegram محسن:** استخدم تنسيق Telegram Markdown البسيط بطريقة احترافية:\n   • العناوين الرئيسية: 🔹 **العنوان**\n   • العناوين الفرعية: 📌 **العنوان الفرعي**\n   • النقاط المهمة: ⚠️ **تنبيه مهم**\n   • الأمثلة: 💡 **مثال**\n   • الخلاصة: ✅ **الخلاصة**\n   • القوائم: استخدم • للنقاط و 1️⃣ 2️⃣ للقوائم المرقمة",
        'chapter_generation_req5': "5. **استخدام الإيموجيات بذكاء:** أضف إيموجيات مناسبة ومتنوعة لجعل المحتوى جذاب وسهل القراءة. استخدم إيموجيات التداول المناسبة (📈, 📉, 📊, 💡, 💰, 🎯, ⚠️, ✅, ❌, 💼, 🔑, 🛡️, 🚀, 💎) بطريقة متوازنة.",
        'chapter_generation_req6': "6. **التركيز على المبتدئين:** تجنب المصطلحات المعقدة أو اشرحها ببساطة. أضف تعريفات سريعة للمصطلحات الجديدة.",
        'chapter_generation_req7': "7. **استخدام اللغة العربية الفصحى أو السعودية:** لا تستخدم اللهجة المصرية أو أي لهجات أخرى. تجنب كلمات مثل (بص، وده، ده، كده، ازاي، ايه، دي، دول). استخدم الفصحى أو اللهجة السعودية فقط.",
        'chapter_generation_important': "**هام جداً:** ابدأ مباشرة بالمحتوى التعليمي دون أي عبارات تمهيدية أو مقدمات مثل 'تمام! إليك شرح' أو 'هذا شرح مفصل' أو أي عبارات مشابهة. اجعل المحتوى منظماً ومقسماً بوضوح مع مسافات مناسبة بين الأقسام. لا تستخدم أي تنسيق خارج Telegram Markdown البسيط. تجنب استخدام الجداول أو الروابط أو code blocks. اجعل المحتوى مناسباً لعرضه في رسالة Telegram واحدة (أقل من 3500 حرف). **مهم جداً: تجنب اللهجة المصرية تماماً واستخدم الفصحى أو السعودية فقط.**",

        'quiz_generation_intro': "أنت مدرس خبير في التداول ومصمم اختبارات محترف. مهمتك هي إنشاء {num_questions} أسئلة اختبار من نوع الاختيار من متعدد بالعربية لتقييم فهم المتعلم للمفاهيم التالية:",
        'quiz_generation_requirements': "**متطلبات الأسئلة:**",
        'quiz_generation_req1': "1. يجب أن تكون الأسئلة متنوعة وتغطي مفاهيم مختلفة من الفصول المذكورة أعلاه.",
        'quiz_generation_req2': "2. كل سؤال يجب أن يكون له 4 خيارات بالضبط، مع إجابة صحيحة واحدة فقط.",
        'quiz_generation_req3': "3. يجب أن تكون الأسئلة واضحة ومباشرة ومناسبة لمستوى المبتدئين.",
        'quiz_generation_req4': "4. يجب أن تكون الخيارات واقعية ومعقولة (لا تضع خيارات سخيفة أو غير منطقية).",
        'quiz_generation_format': "**تنسيق الإخراج المطلوب:**",
        'quiz_generation_format_note': "ملاحظة: تأكد من أن correct_option_id هو رقم صحيح يمثل فهرس الخيار الصحيح في مصفوفة options (0 للخيار الأول، 1 للخيار الثاني، إلخ).",
        'quiz_generation_json_only': "أنتج JSON صالح فقط بدون أي نص إضافي أو شرح.",

        'review_material_intro': "أنت مدرس خبير في التداول. المستخدم يحتاج إلى مراجعة المفاهيم التالية التي واجه صعوبة فيها خلال الاختبار:",
        'review_material_instruction': "قم بإنشاء ملخص مراجعة موجز وواضح بالعربية يغطي هذه المفاهيم. استخدم تنسيق نصي بسيط (بدون Markdown معقد) واستخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'review_material_should_include': "يجب أن يتضمن الملخص:",
        'review_material_point1': "1. شرح مبسط للمفاهيم الأساسية",
        'review_material_point2': "2. نقاط رئيسية يجب تذكرها",
        'review_material_point3': "3. أمثلة توضيحية قصيرة",
        'review_material_point4': "4. نصائح عملية للتطبيق",
        'review_material_concise': "اجعل المحتوى موجزًا ومركزًا ومفيدًا للمراجعة السريعة.",
        'review_material_formatting_notes': "ملاحظات مهمة للتنسيق:",
        'review_material_format1': "- تجنب استخدام علامات Markdown المعقدة",
        'review_material_format2': "- استخدم النجمة (*) للنقاط فقط",
        'review_material_format3': "- تجنب استخدام الروابط",
        'review_material_format4': "- تجنب استخدام الجداول",
        'review_material_format5': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة",
        'review_material_format6': "- استخدم علامات النجمة المزدوجة (**) للتنسيق الغامق بشكل صحيح",
        'review_material_format7': "- استخدم الإيموجي بشكل معتدل",

        'supplementary_chapter_intro': "أنت مدرس خبير في التداول ومؤلف محتوى تعليمي. مهمتك هي إنشاء فصل تكميلي مخصص للمتعلم بالعربية.",
        'supplementary_chapter_info': "**معلومات الفصل:**",
        'supplementary_chapter_title': "- العنوان: {title}",
        'supplementary_chapter_description': "- الوصف: {description}",
        'supplementary_chapter_level': "- المستوى: {level}",
        'supplementary_chapter_topic': "- الموضوع: {topic}",
        'supplementary_chapter_content_instructions': "**تعليمات المحتوى:**",
        'supplementary_chapter_inst1': "1. قم بإنشاء محتوى تعليمي موجز وفعال حول الموضوع المحدد.",
        'supplementary_chapter_inst2': "2. استخدم لغة واضحة ومباشرة تناسب مستوى المتعلم ({level}).",
        'supplementary_chapter_inst3': "3. قسّم المحتوى إلى أقسام منطقية مع عناوين فرعية.",
        'supplementary_chapter_inst4': "4. أضف أمثلة عملية موجزة لتوضيح المفاهيم.",
        'supplementary_chapter_inst5': "5. استخدم الإيموجي المناسبة لجعل المحتوى أكثر جاذبية.",
        'supplementary_chapter_inst6': "6. اختم بملخص قصير للنقاط الرئيسية.",
        'supplementary_chapter_format': "**تنسيق المحتوى:**",
        'supplementary_chapter_format1': "- استخدم تنسيق نصي بسيط (تجنب Markdown المعقد).",
        'supplementary_chapter_format2': "- استخدم الإيموجي بشكل مناسب في بداية كل قسم.",
        'supplementary_chapter_format3': "- تجنب استخدام الرموز الخاصة مثل _ و ~ و ` و | و > بكثرة.",
        'supplementary_chapter_format4': "- تجنب استخدام الروابط والجداول.",
        'supplementary_chapter_format5': "- استخدم علامات النجمة المزدوجة (**) للتنسيق الغامق بشكل صحيح.",
        'supplementary_chapter_format6': "- يجب أن يكون المحتوى موجزًا ولا يتجاوز 3000 حرف.",
        'supplementary_chapter_conclusion': "أنشئ محتوى تعليميًا موجزًا وفعالًا يساعد المتعلم على فهم الموضوع بشكل أفضل.",
        'quiz_timeout': "⏰ انتهى الوقت المحدد للإجابة على السؤال.",
        'psychology_title': "علم النفس في التداول",
        'psychology_desc': "فهم العوامل النفسية وتأثيرها على قرارات التداول",
        'strategies_title': "استراتيجيات التداول المتقدمة",
        'strategies_desc': "استراتيجيات متقدمة للتداول في مختلف ظروف السوق",
        'adv_risk_title': "إدارة المخاطر المتقدمة",
        'adv_risk_desc': "تقنيات متقدمة لإدارة المخاطر وحماية رأس المال",
        'content_too_long': "المحتوى طويل وسيتم إرساله في عدة رسائل:",

        # رؤوس الرسائل المقسمة للمدرس الذكي
        'ai_response_part_header': "📚 **إجابة المدرس الذكي** (الجزء {part_num} من {total_parts})",
        'ai_response_continuation_header': "📖 **تكملة الإجابة** (الجزء {part_num} من {total_parts})",
        'ai_conversation_part_header': "🤖 **محادثة مع المدرس الذكي** (الجزء {part_num} من {total_parts})",
        'ai_conversation_continuation_header': "💬 **تكملة المحادثة** (الجزء {part_num} من {total_parts})",

        # Add other keys from trading_education.py as needed
    },
    'en': {
        # Main menu translations
        'welcome': """🌟 Welcome to Technical Analysis Bot! 🌟

A specialized bot for analyzing cryptocurrencies using AI and advanced technical indicators.

📊 **Key Features:**
• Unified Enhanced Analysis (combines 4 analysis styles)
• Smart price alerts
• Learn trading with AI
• Multi-platform support (Binance, KuCoin, etc.)

Analyses: 3/3 🔍
Alerts: 1/1 🔔
Next Free Day: Monday (09-06-2025) 🎁

🔑 API: Binance ✅ KuCoin ❌ Coinbase ❌ Bybit ❌ OKX ❌ Kraken ❌

✨ **Get additional features (5 USD weekly):**
• Smart Automated Trading System 🤖
• Unlimited price alerts 🔔
• Advanced customization ⚙️
• Unlimited analyses 📈
• All technical indicators 🎯
• Gemini AI analysis 🤖

Click ✨ Upgrade Account to subscribe""",
        'features': "Available Features:",
        'analyze': "📊 Enhanced Analysis",
        'active_alerts': "🔔 Active Alerts",
        'help': "💡 Help",
        'change_language': "🌐 Change Language",
        'manage_currencies': "💱 Manage Currencies",
        'back_to_main': "🔙 Main Menu",

        # Trading education translations
        'welcome_trading_ai': "Welcome to the AI Trading Learning Course! 📈",
        'add_gemini_key_prompt': "To get started, please add your Gemini API key. This will allow me to generate personalized educational content for you.",
        'add_gemini_key_button': "🔑 Add Gemini API Key",
        'gemini_access_error': "Sorry, I can't access Gemini right now. Please ensure you have added a valid API key.",
        'gemini_tutor_error': "An error occurred while trying to answer your question. Please try again later.",
        'lesson_generation_error': "Sorry, I can't access Gemini right now to generate the lesson. Please ensure you have added a valid API key.",
        'quota_exceeded_error': "Gemini free tier quota exceeded. Please try again later or upgrade your account in Google AI Studio.",
        'chapter_button': "Chapter {number}",
        'next_chapter_button': "Next Chapter ❯",
        'take_quiz_button': "📝 Take the Quiz",
        'ask_tutor_prompt': "You can also ask any trading-related question, and I'll do my best to answer it using Gemini.",
        'ask_tutor_button': '❓ Ask AI Tutor',
        'ask_tutor_instruction': "Please type your trading question.",
        'quiz_already_started': "📝 It looks like you've already started the quiz. Please complete answering the questions.",
        'course_completed': "You have completed the basic course and the quiz! 🎉",
        'error_starting_course': "Something went wrong. Please try starting the course again using /learn_trading_ai",
        'tutor_prompt_header': "You are an expert trading tutor. The user is currently studying chapter {chapter_number} about '{chapter_topic}'. Answer their following question in English with focus on concepts related to this topic as much as possible:\n\nQuestion: {question}\n\nAnswer:",
        'ai_conversation_system_instruction': "You are an educational assistant specialized in cryptocurrency trading. The user is currently studying the chapter related to '{chapter_topic}'. Answer the user's questions related to this topic or trading topics in general in English. Keep your answers clear, concise, and appropriate for beginners. Use Markdown formatting when needed.",
        'enhanced_tutor_prompt': "Please provide a detailed and comprehensive answer. Very short responses are not acceptable. Your answer must include:\n1. Detailed explanation of concepts related to the question\n2. Practical examples illustrating the idea\n3. Tips and guidance for beginner traders\n4. Additional useful information related to the topic\n\nThe answer should be comprehensive and cover all aspects of the question in depth.",
        'enhanced_chapter_prompt': "Please provide detailed and comprehensive educational content. Very short content is not acceptable. Your content must include:\n1. Detailed explanation of the core concepts in this chapter\n2. Practical examples illustrating the concepts\n3. Tips and guidance for beginner traders\n4. Additional useful information related to the topic\n5. Appropriate use of emojis\n\nThe content should be comprehensive and cover all aspects of the topic in depth, while following the requested format.",

        # Supplementary Chapters Titles and Descriptions - English
        'basics_chapter_title': "Trading Basics - Comprehensive Review",
        'basics_chapter_desc': "Comprehensive review of fundamental trading concepts",
        'risk_management_title': "Risk Management for Beginners",
        'risk_management_desc': "Learn how to protect your capital and minimize losses",
        'intermediate_strategies_title': "Intermediate Trading Strategies",
        'intermediate_strategies_desc': "Advanced strategies for traders with intermediate experience",
        'technical_analysis_title': "Advanced Technical Analysis",
        'technical_analysis_desc': "Deep understanding of technical indicators and patterns",
        'advanced_strategies_title': "Advanced Trading Strategies",
        'advanced_strategies_desc': "Advanced strategies for professional traders",
        'market_psychology_title': "Market Psychology and Emotional Trading",
        'market_psychology_desc': "Understanding psychological factors that affect trading decisions",
        'indicators_title': "Deep Dive into Technical Indicators",
        'indicators_desc': "Detailed explanation of technical indicators and how to use them",
        'general_trading_title': "Basic Trading Concepts",
        'general_trading_desc': "Comprehensive review of basic trading concepts",

        # AI Learning Content Generation Prompts
        'chapter_generation_intro': "You are an expert in cryptocurrency trading education and professional educational content designer. Explain the following topic for beginners in an organized and engaging way:",
        'chapter_generation_topic': "**Topic:** {topic}",
        'chapter_generation_requirements': "**Requirements:**",
        'chapter_generation_req1': "1. **Clear and organized structure:** Divide content into clear sections with distinctive subheadings. Start with a brief introduction, then detailed explanation, and end with summary or key points.",
        'chapter_generation_req2': "2. **Detailed and clear explanation:** Use simple and direct language with logical progression from simple to complex information.",
        'chapter_generation_req3': "3. **Practical and applied examples:** Provide real-world examples and practical cases to illustrate concepts with numbers and real situations.",
        'chapter_generation_req4': "4. **Enhanced Telegram formatting:** Use simple Telegram Markdown formatting professionally:\n   • Main headings: 🔹 **Heading**\n   • Subheadings: 📌 **Subheading**\n   • Important points: ⚠️ **Important Note**\n   • Examples: 💡 **Example**\n   • Summary: ✅ **Summary**\n   • Lists: Use • for bullet points and 1️⃣ 2️⃣ for numbered lists",
        'chapter_generation_req5': "5. **Smart use of emojis:** Add appropriate and varied emojis to make content attractive and easy to read. Use suitable trading emojis (📈, 📉, 📊, 💡, 💰, 🎯, ⚠️, ✅, ❌, 💼, 🔑, 🛡️, 🚀, 💎) in a balanced way.",
        'chapter_generation_req6': "6. **Focus on beginners:** Avoid complex terminology or explain it simply. Add quick definitions for new terms.",
        'chapter_generation_req7': "7. **Respond in English only:** Do not use any other language in the content.",
        'chapter_generation_important': "**Very Important:** Start directly with the educational content without any introductory phrases or preambles like 'Here's a detailed explanation' or 'This is a comprehensive guide' or any similar phrases. Make content organized and clearly divided with appropriate spacing between sections. Do not use any formatting outside simple Telegram Markdown. Avoid using tables, links, or code blocks. Make content suitable for display in a single Telegram message (less than 3500 characters).",

        'quiz_generation_intro': "You are an expert trading teacher and professional quiz designer. Your task is to create {num_questions} multiple-choice quiz questions in English to assess the learner's understanding of the following concepts:",
        'quiz_generation_requirements': "**Question Requirements:**",
        'quiz_generation_req1': "1. Questions should be diverse and cover different concepts from the chapters mentioned above.",
        'quiz_generation_req2': "2. Each question must have exactly 4 options, with only one correct answer.",
        'quiz_generation_req3': "3. Questions should be clear, direct, and appropriate for beginner level.",
        'quiz_generation_req4': "4. Options should be realistic and reasonable (don't include silly or illogical options).",
        'quiz_generation_format': "**Required Output Format:**",
        'quiz_generation_format_note': "Note: Make sure correct_option_id is an integer representing the index of the correct option in the options array (0 for first option, 1 for second option, etc).",
        'quiz_generation_json_only': "Produce valid JSON only without any additional text or explanation.",

        'review_material_intro': "You are an expert trading teacher. The user needs to review the following concepts that they had difficulty with during the quiz:",
        'review_material_instruction': "Create a brief and clear review summary in English covering these concepts. Use simple text formatting (without complex Markdown) and use appropriate emojis to make the content more attractive.",
        'review_material_should_include': "The summary should include:",
        'review_material_point1': "1. Simplified explanation of basic concepts",
        'review_material_point2': "2. Key points to remember",
        'review_material_point3': "3. Short illustrative examples",
        'review_material_point4': "4. Practical tips for application",
        'review_material_concise': "Make the content concise, focused, and useful for quick review.",
        'review_material_formatting_notes': "Important formatting notes:",
        'review_material_format1': "- Avoid using complex Markdown tags",
        'review_material_format2': "- Use asterisk (*) for bullet points only",
        'review_material_format3': "- Avoid using links",
        'review_material_format4': "- Avoid using tables",
        'review_material_format5': "- Avoid using special characters like _ and ~ and ` and | and > extensively",
        'review_material_format6': "- Avoid using double asterisks (**) for formatting",
        'review_material_format7': "- Use emojis moderately",

        'supplementary_chapter_intro': "You are an expert trading teacher and educational content author. Your task is to create a customized supplementary chapter for the learner in English.",
        'supplementary_chapter_info': "**Chapter Information:**",
        'supplementary_chapter_title': "- Title: {title}",
        'supplementary_chapter_description': "- Description: {description}",
        'supplementary_chapter_level': "- Level: {level}",
        'supplementary_chapter_topic': "- Topic: {topic}",
        'supplementary_chapter_content_instructions': "**Content Instructions:**",
        'supplementary_chapter_inst1': "1. Create concise and effective educational content on the specified topic.",
        'supplementary_chapter_inst2': "2. Use clear and direct language appropriate for the learner's level ({level}).",
        'supplementary_chapter_inst3': "3. Divide content into logical sections with subheadings.",
        'supplementary_chapter_inst4': "4. Add brief practical examples to illustrate concepts.",
        'supplementary_chapter_inst5': "5. Use appropriate emojis to make content more attractive.",
        'supplementary_chapter_inst6': "6. End with a brief summary of key points.",
        'supplementary_chapter_format': "**Content Format:**",
        'supplementary_chapter_format1': "- Use simple text formatting (avoid complex Markdown).",
        'supplementary_chapter_format2': "- Use emojis appropriately at the beginning of each section.",
        'supplementary_chapter_format3': "- Avoid using special characters like _ and ~ and ` and | and > extensively.",
        'supplementary_chapter_format4': "- Avoid using links and tables.",
        'supplementary_chapter_format5': "- Avoid using double asterisks (**) for formatting.",
        'supplementary_chapter_format6': "- Content should be concise and not exceed 3000 characters.",
        'supplementary_chapter_conclusion': "Create concise and effective educational content that helps the learner understand the topic better.",
        'quiz_timeout': "⏰ Time is up for answering the question.",
        'chapter_gen_prompt_header': "You are an expert trading tutor and educational content creator in English. Your task is to create educational content for chapter {chapter_number} of a 10-chapter beginner trading course.",
        'psychology_title': "Trading Psychology",
        'psychology_desc': "Understanding psychological factors and their impact on trading decisions",
        'strategies_title': "Advanced Trading Strategies",
        'strategies_desc': "Advanced strategies for trading in different market conditions",
        'adv_risk_title': "Advanced Risk Management",
        'adv_risk_desc': "Advanced techniques for risk management and capital protection",
        'content_too_long': "Content is long and will be sent in multiple messages:",
        'guide_add_gemini_key': "Please use the /add_gemini_key command to add your API key.",
        'complete_chapters_first': "Please complete all chapters before taking the quiz.",
        'quiz_starting': "🎯 Starting the quiz!\n\nQuestions will be sent one by one. You have 30 seconds to answer each question.",
        'generating_quiz': "⏳ Generating quiz...",
        'quiz_results': "Quiz Results",
        'correct_answers': "Correct Answers",
        'percentage': "Percentage",
        'excellent_result': "🌟 Excellent! You have an excellent understanding of the concepts.",
        'good_result': "👍 Good! You have a good understanding of the basic concepts.",
        'average_result': "🤔 Average. There are some concepts that need review.",
        'needs_improvement': "📚 You need more study. Don't worry, keep learning!",
        'results_error': "An error occurred while displaying results. Please try again.",
        'supplementary_chapters': "📚 Customized Supplementary Chapters",
        'back_to_main': "🔙 Back to Main Menu",
        'next_steps': "What would you like to do now?",
        'complete_quiz_first': "Please complete the quiz first to get customized supplementary chapters.",
        'no_supplementary_chapters': "No supplementary chapters are currently available. Please try again later.",
        'supplementary_chapters_list': "📚 Customized supplementary chapters for you based on your quiz results:",
        'back_button': "🔙 Back",
        'review_material_title': "Review Material",
        'quiz_score': "Score",
        'quiz_performance': "Performance",
        'ask_again_prompt': "You can ask another question or proceed to the next chapter.",
        'operation_in_progress': "⏳ Generating chapter... Please wait.",
        'avoid_multiple_clicks': "⚠️ Please avoid clicking the button multiple times to prevent creating duplicate chapters.",
        'quiz_generation_in_progress': "⏳ Generating quiz... Please wait.",
        'continue_or_ask': "You can now continue with the chapter or ask another question.",
        'review_material_title': "Review Materials",
        'supplementary_chapters': "📚 Customized Supplementary Chapters",
        'back_to_main': "🏠 Back to Main Menu",
        'next_steps': "What would you like to do now?",
        'generating_chapter': "⏳ Generating supplementary chapter...",
        'generating_main_chapter': "⏳ Creating a custom chapter for you...",
        'preparing_content': "⏳ Preparing educational content...",
        'chapter_not_found': "The requested chapter was not found.",
        'chapter_generation_error': "An error occurred while generating the supplementary chapter. Please try again later.",
        'back_to_chapters': "🔙 Back to Chapters List",
        'no_supplementary_chapters': "No supplementary chapters are available at this time. Please try again later.",
        'supplementary_chapters_list': "📚 Customized supplementary chapters based on your quiz results:",
        'back_button': "🔙 Back",
        'review_generation_error': "An error occurred while generating review materials. Please try again later.",
        # Terms and Conditions
        'terms_and_conditions_title': "📜 Terms and Conditions",
        'terms_and_conditions_content': "**Terms and Conditions for Using the Trading Analysis Bot**\n\n1. **General Use**: This bot is designed to provide cryptocurrency trading analysis and recommendations for informational purposes only.\n\n2. **Disclaimer**: The information provided is not financial advice, and we are not responsible for any losses that may result from using this information.\n\n3. **Subscription**: Some features are only available to subscribed users. Subscriptions are automatically renewed unless canceled.\n\n4. **Privacy**: We collect limited data to improve your experience. We will not share your personal information with third parties without your consent.\n\n5. **API**: When adding your API keys, you agree that we use them only to access your account data to provide the requested services.\n\n6. **Changes**: We reserve the right to modify these terms at any time. Users will be notified of significant changes.",
        'agree_button': "✅ I Agree to Terms",
        'disagree_button': "❌ I Disagree",
        'terms_accepted': "Thank you for accepting the terms and conditions",
        'terms_declined': "Unfortunately, you must agree to the terms to use the bot",
        'terms_required': "Unfortunately, you must agree to the terms to use the bot. You can restart the bot at any time to agree to the terms. 🔄",
        'language_selected': "Language selected successfully",
        'error_saving_settings': "An error occurred while saving settings. Please try again",

        # Headers for split messages from AI tutor
        'ai_response_part_header': "📚 **AI Tutor Response** (Part {part_num} of {total_parts})",
        'ai_response_continuation_header': "📖 **Response Continuation** (Part {part_num} of {total_parts})",
        'ai_conversation_part_header': "🤖 **AI Tutor Conversation** (Part {part_num} of {total_parts})",
        'ai_conversation_continuation_header': "💬 **Conversation Continuation** (Part {part_num} of {total_parts})",

        # Add other keys from trading_education.py as needed
    }
}

def get_text(key: str, lang: str = 'ar', default: str = None, **kwargs) -> str:
    """Gets translated text, falling back to Arabic, then default, then the key itself."""
    try:
        # Try the requested language
        text = translations.get(lang, {}).get(key)
        # If not found, try Arabic
        if text is None:
            text = translations.get('ar', {}).get(key)
        # If still not found, use the provided default or the key itself
        if text is None:
            text = default if default is not None else f"[{key}]"
            logger.warning(f"Translation key '{key}' not found for lang '{lang}' or 'ar'. Used default/key.")

        return text.format(**kwargs)
    except KeyError as e:
        logger.error(f"Missing format key {e} for translation key '{key}' in lang '{lang}'")
        return default if default is not None else f"[{key} - FORMAT ERROR]"
    except Exception as e:
        logger.error(f"Error in get_text for key '{key}', lang '{lang}': {e}")
        return default if default is not None else f"[{key} - ERROR]"

def fix_bold_formatting(text: str, lang: str = 'ar') -> str:
    """
    إصلاح تنسيق النص العريض في تلغرام وإزالة مشاكل Markdown

    يقوم بإصلاح مشاكل التنسيق الشائعة مع الحفاظ على التنسيق الغامق الصحيح
    مثال: **عنوان:** -> **عنوان**:
    كما يقوم بإزالة أي code blocks غير مرغوب فيها

    Args:
        text: النص المراد إصلاحه
        lang: لغة النص (ar أو en) لتطبيق قواعد مختلفة حسب اللغة

    Returns:
        النص بعد إصلاح التنسيق
    """
    if not text:
        return text

    # إزالة أي بادئات أو لواحق غير متوقعة قد تسبب مشاكل في التحليل (مثل ```markdown)
    text = text.strip()
    if text.startswith("```markdown"):
        text = text[len("```markdown"):].strip()
    if text.startswith("```"):
        text = text[3:].strip()
    if text.endswith("```"):
        text = text[:-3].strip()

    # إزالة بادئات نموذج الذكاء الاصطناعي الشائعة
    ai_prefixes = [
        # English patterns
        r"^Here's an explanation of .* for beginners, formatted for Telegram:\s*",
        r"^Here is the explanation:\s*",
        r"^Here's your introduction to .* for beginners!\s*",
        r"^Here's .* formatted for Telegram:\s*",
        r"^Here is .* content:\s*",
        r"^Here's the .* chapter:\s*",
        r"^Here's a detailed explanation.*:\s*",
        r"^This is a comprehensive guide.*:\s*",

        # Arabic patterns - العبارات التمهيدية العربية
        r"^تمام!?\s*إليك شرح.*:\s*",
        r"^تمام!?\s*إليك.*:\s*",
        r"^إليك شرح .* للمبتدئين.*:\s*",
        r"^إليك شرح مبسط ومفصل.*:\s*",
        r"^إليك شرح مفصل.*:\s*",
        r"^إليك المحتوى.*:\s*",
        r"^هذا هو الفصل.*:\s*",
        r"^إليك الفصل.*:\s*",
        r"^هذا شرح مفصل.*:\s*",
        r"^سأقدم لك شرح.*:\s*",
        r"^سأشرح لك.*:\s*",
        r"^بالطبع!?\s*إليك.*:\s*",
        r"^ممتاز!?\s*إليك.*:\s*",
        r"^حسناً،?\s*إليك.*:\s*",
        r"^إليك.*بتنسيق Telegram.*:\s*",
        r"^إليك.*مع الإيموجيات.*:\s*"
    ]

    for prefix_pattern in ai_prefixes:
        text = re.sub(prefix_pattern, "", text, flags=re.IGNORECASE | re.MULTILINE)

    # إزالة النجوم الزائدة وتحسين التنسيق
    # استبدال النجوم المتعددة بتنسيق bold بسيط
    text = re.sub(r'\*{3,}([^*]+)\*{3,}', r'**\1**', text)  # *** أو أكثر إلى **
    text = re.sub(r'\*{5,}', '**', text)  # إزالة النجوم الزائدة المفردة
    text = re.sub(r'\*{3,4}', '**', text)  # تحويل *** أو **** إلى **

    # إصلاح النقطتين في النص العريض أولاً - تطبيق عالمي لجميع اللغات
    # استبدال أي عنوان يحتوي على نقطتين داخل النص العريض
    # مثال: **نصائح عامة:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?):\*\*', r'**\1**:', text)

    # إصلاح النقطتين المزدوجة في النص العريض
    # مثال: **نصائح عامة**:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?)\*\*:\*\*', r'**\1**:', text)

    # إصلاح النص العريض المكسور - تحويل * المفردة إلى ** للنص العريض
    # بدلاً من إزالة النجوم المفردة، نحولها إلى نجوم مزدوجة للنص العريض
    # هذا يحافظ على النص العريض المطلوب من المستخدم

    # تعطيل معالجة النجوم المفردة لتجنب تشويه النصوص العربية
    # هذا السطر كان يسبب مشاكل في تقطيع النصوص العربية مثل "**تحل**ي**ل**"
    # سيتم الاعتماد على النجوم المزدوجة الموجودة أصلاً من Gemini
    # if text.count('*') <= 4 and '\n*   ' not in text:
    #     text = re.sub(r'(?<!\*)\*([^*\s]+)\*(?!\*)', r'**\1**', text)

    # تعطيل إصلاحات النجوم المعقدة لتجنب تشويه النصوص العربية
    # هذه الإصلاحات كانت تسبب مشاكل في تقطيع النصوص العربية
    # سيتم الاعتماد على التنسيق الأصلي من Gemini

    # إصلاح النص العريض غير المكتمل (نجمة واحدة في البداية أو النهاية) - معطل
    # text = re.sub(r'^\*([^*])', r'**\1', text)  # إصلاح * في البداية
    # text = re.sub(r'([^*])\*$', r'\1**', text)  # إصلاح * في النهاية

    # إصلاح النص العريض المكسور في وسط النص - معطل
    # البحث عن أنماط مثل "**نص" أو "نص**" وإصلاحها
    # text = re.sub(r'(\s)\*\*([^*\s][^*]*?)(\s)', r'\1**\2**\3', text)  # **نص + مسافة
    # text = re.sub(r'(\s)([^*\s][^*]*?)\*\*(\s)', r'\1**\2**\3', text)  # مسافة + نص**

    # إصلاح النجوم المكسورة في بداية الأسطر - معطل
    # text = re.sub(r'^(\*\*[^*]+?)\s+(\*\*)', r'\1\2', text, flags=re.MULTILINE)

    # إصلاح النجوم المكسورة بين الكلمات - معطل
    # text = re.sub(r'(\*\*[^*]*?)\s+(\*\*[^*]*?)\s+(\*\*)', r'\1 \2\3', text)

    # إصلاح النمط المكسور مثل "**كلمة **كلمة أخرى**" - معطل
    # text = re.sub(r'\*\*([^*]+?)\s+\*\*([^*]+?)\*\*', r'**\1** **\2**', text)

    # تعطيل إضافة النجوم التلقائية لتجنب تشويه النصوص
    # هذا كان يسبب مشاكل في النصوص العربية
    # التأكد من أن النص العريض مغلق بشكل صحيح - معطل
    # عد عدد ** في النص
    # bold_count = text.count('**')
    # if bold_count % 2 != 0:
    #     # إذا كان العدد فردي، أضف ** في النهاية
    #     text += '**'

    # تحسين التباعد والتنسيق للقابلية للقراءة
    text = improve_text_spacing_for_readability(text, lang)

    # إزالة الأسطر الفارغة المتكررة
    text = re.sub(r'\n\n\n+', '\n\n', text)

    return text

def improve_text_spacing_for_readability(text: str, lang: str = 'ar') -> str:
    """
    تحسين التباعد والتنسيق لجعل النص أكثر قابلية للقراءة في تلغرام - للنصوص الطويلة فقط

    Args:
        text: النص المراد تحسينه
        lang: لغة النص (ar أو en)

    Returns:
        النص بعد تحسين التباعد
    """
    if not text:
        return text

    # تطبيق التحسينات فقط للنصوص الطويلة (أكثر من 800 حرف)
    if len(text) < 800:
        return text

    # إضافة مسافة إضافية بعد العناوين المنسقة بالإيموجي فقط إذا لم تكن موجودة
    text = re.sub(r'(🔹|📌|⚠️|💡|✅|🤖|💬|📚|📖)\s*\*\*([^*]+)\*\*\n([^\n\s])', r'\1 **\2**\n\n\3', text)

    # إضافة مسافة قبل النقاط والقوائم فقط إذا لم تكن موجودة
    text = re.sub(r'([^\n])\n(•|\d+\.)', r'\1\n\n\2', text)

    # إضافة مسافة بعد الفقرات الطويلة فقط (أكثر من 80 حرف)
    text = re.sub(r'([.!؟?])(\n)([^•\-\d\n🔹📌⚠️💡✅🤖💬📚📖\s][^\n]{80,})', r'\1\2\n\3', text)

    # تحسين التباعد حول النصوص العريضة فقط للنصوص الطويلة
    text = re.sub(r'([^\n])\n(\*\*[^*]{20,}\*\*)', r'\1\n\n\2', text)
    text = re.sub(r'(\*\*[^*]{20,}\*\*)\n([^\n\s])', r'\1\n\n\2', text)

    # تنظيف المسافات المتكررة (أكثر من سطرين فارغين)
    text = re.sub(r'\n\n\n+', '\n\n', text)

    return text

def clean_egyptian_dialect(text: str) -> str:
    """
    تنظيف النص من اللهجة المصرية واستبدالها بالفصحى أو السعودية

    Args:
        text: النص المراد تنظيفه

    Returns:
        النص بعد إزالة اللهجة المصرية
    """
    if not text:
        return text

    # قاموس استبدال الكلمات المصرية بالفصحى/السعودية
    egyptian_to_standard = {
        # الكلمات المصرية الشائعة
        r'\bبص\b': 'انظر',
        r'\bوده\b': 'هذا',
        r'\bده\b': 'هذا',
        r'\bدي\b': 'هذه',
        r'\bدول\b': 'هؤلاء',
        r'\bكده\b': 'هكذا',
        r'\bازاي\b': 'كيف',
        r'\bايه\b': 'ماذا',
        r'\bعايز\b': 'تريد',
        r'\bعاوز\b': 'تريد',
        r'\bهيك\b': 'هكذا',
        r'\bكدا\b': 'هكذا',
        r'\bلسه\b': 'لا يزال',
        r'\bخلاص\b': 'انتهى',
        r'\bيلا\b': 'هيا',
        r'\bماشي\b': 'حسناً',
        r'\bاهو\b': 'ها هو',
        r'\bاهي\b': 'ها هي',
        r'\bعشان\b': 'لأن',
        r'\bلحد\b': 'حتى',
        r'\bبقى\b': 'أصبح',
        r'\bقال ايه\b': 'ماذا قال',
        r'\bعمل ايه\b': 'ماذا فعل',

        # عبارات مصرية شائعة
        r'\bبص يا\b': 'انظر يا',
        r'\bشوف كده\b': 'انظر هكذا',
        r'\bايه ده\b': 'ما هذا',
        r'\bده اللي\b': 'هذا الذي',
        r'\bدي اللي\b': 'هذه التي',
        r'\bكده يعني\b': 'هكذا أي',
        r'\bوده معناه\b': 'وهذا معناه',
        r'\bده هيخلي\b': 'هذا سيجعل',
        r'\bدي هتساعد\b': 'هذه ستساعد',
    }

    # تطبيق الاستبدالات
    for egyptian_pattern, standard_replacement in egyptian_to_standard.items():
        text = re.sub(egyptian_pattern, standard_replacement, text, flags=re.IGNORECASE)

    return text

def clean_markdown_content(text: str, lang: str = 'ar') -> str:
    """
    تنظيف محتوى Markdown لجعله مناسباً لـ Telegram مع تحسين التنسيق والهيكلة

    Args:
        text: النص المراد تنظيفه
        lang: لغة النص (ar أو en) لتطبيق قواعد مختلفة حسب اللغة

    Returns:
        النص بعد التنظيف والتحسين
    """
    if not text:
        return text

    # إزالة النجوم الزائدة وتحسين التنسيق أولاً
    # استبدال النجوم المتعددة بتنسيق bold بسيط
    text = re.sub(r'\*{3,}([^*]+)\*{3,}', r'**\1**', text)  # *** أو أكثر إلى **
    text = re.sub(r'\*{5,}', '**', text)  # إزالة النجوم الزائدة

    # إصلاح النص العريض المكسور
    text = re.sub(r'\*\*([^*\n]+)(?!\*\*)', r'**\1**', text)

    # إزالة أي code blocks غير مرغوب فيها وإصلاح التنسيق
    text = fix_bold_formatting(text, lang)

    # تنظيف اللهجة المصرية للنصوص العربية
    if lang == 'ar':
        text = clean_egyptian_dialect(text)

    # إزالة عبارات تمهيدية إضافية قد تكون في وسط النص
    if lang == 'ar':
        additional_arabic_patterns = [
            r"تمام!?\s*سأقوم بشرح.*\n",
            r"بالطبع!?\s*سأوضح لك.*\n",
            r"ممتاز!?\s*دعني أشرح.*\n",
            r"حسناً،?\s*سأبدأ بشرح.*\n",
            r"الآن سأقدم لك.*\n",
            r"فيما يلي شرح.*\n",
            r"سأشرح لك.*\n",
            r"دعني أوضح.*\n",
            r"إليك شرح.*\n"
        ]
        for pattern in additional_arabic_patterns:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)

    # تحسين هيكلة المحتوى
    text = improve_content_structure(text, lang)

    # إزالة أي تنسيق معقد غير مدعوم في Telegram
    # إزالة الجداول البسيطة
    text = re.sub(r'\|.*?\|', '', text)

    # إزالة الروابط المعقدة وترك النص فقط
    text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)

    # إزالة أي HTML tags متبقية
    text = re.sub(r'<[^>]+>', '', text)

    # تحسين التباعد والتنسيق النهائي
    text = improve_spacing_and_formatting(text, lang)

    # إزالة المسافات الزائدة في بداية ونهاية النص
    text = text.strip()

    return text

def improve_content_structure(text: str, lang: str = 'ar') -> str:
    """
    تحسين هيكلة المحتوى التعليمي لجعله أكثر وضوحاً وتنظيماً
    """
    if not text:
        return text

    # تحسين العناوين والأقسام
    if lang == 'ar':
        # تحسين العناوين الرئيسية
        text = re.sub(r'^([^\n]*(?:مقدمة|تعريف|أساسيات|مفهوم|شرح)[^\n]*):?\s*$', r'🔹 **\1**', text, flags=re.MULTILINE)

        # تحسين العناوين الفرعية
        text = re.sub(r'^([^\n]*(?:أنواع|طرق|استراتيجيات|خطوات|مراحل|عوامل)[^\n]*):?\s*$', r'📌 **\1**', text, flags=re.MULTILINE)

        # تحسين النقاط المهمة
        text = re.sub(r'^([^\n]*(?:مهم|ملاحظة|تنبيه|انتباه|احذر)[^\n]*):?\s*$', r'⚠️ **\1**', text, flags=re.MULTILINE)

        # تحسين الأمثلة
        text = re.sub(r'^([^\n]*(?:مثال|مثلاً|على سبيل المثال)[^\n]*):?\s*$', r'💡 **\1**', text, flags=re.MULTILINE)

        # تحسين الخلاصة والنتائج
        text = re.sub(r'^([^\n]*(?:خلاصة|نتيجة|استنتاج|الخلاصة)[^\n]*):?\s*$', r'✅ **\1**', text, flags=re.MULTILINE)

    # تحسين القوائم والنقاط
    # تحويل النقاط البسيطة إلى نقاط منسقة
    text = re.sub(r'^[-•]\s*(.+)$', r'• \1', text, flags=re.MULTILINE)
    text = re.sub(r'^(\d+)[\.\)]\s*(.+)$', r'\1️⃣ \2', text, flags=re.MULTILINE)

    return text

def improve_spacing_and_formatting(text: str, lang: str = 'ar') -> str:
    """
    تحسين التباعد والتنسيق النهائي للمحتوى
    """
    if not text:
        return text

    # إضافة مسافة بعد العناوين المنسقة
    text = re.sub(r'(🔹|📌|⚠️|💡|✅)\s*\*\*([^*]+)\*\*\s*\n', r'\1 **\2**\n\n', text)

    # تحسين التباعد حول النقاط
    text = re.sub(r'\n(•|\d+️⃣)', r'\n\n\1', text)

    # إضافة مسافة قبل الفقرات المهمة
    text = re.sub(r'\n([^•\d\n🔹📌⚠️💡✅][^\n]{50,})', r'\n\n\1', text)

    # تنظيف الأسطر الفارغة المتكررة (أكثر من سطرين)
    text = re.sub(r'\n\n\n+', '\n\n', text)

    # إضافة مسافة في نهاية الأقسام الرئيسية
    text = re.sub(r'([.!؟])\n([🔹📌⚠️💡✅])', r'\1\n\n\2', text)

    return text

def fix_arabic_text_spacing(text: str) -> str:
    """
    إصلاح مشكلة تباعد النصوص العربية في المحتوى المُولد بالذكاء الاصطناعي

    Args:
        text: النص المراد إصلاحه

    Returns:
        النص بعد إصلاح التباعد
    """
    if not text:
        return text

    # قائمة الكلمات والعبارات الشائعة التي تحتاج إصلاح تباعد
    spacing_fixes = {
        # كلمات مدمجة شائعة
        r'مقدمةفي': 'مقدمة في',
        r'مقدمةإلى': 'مقدمة إلى',
        r'ماهو': 'ما هو',
        r'ماهي': 'ما هي',
        r'هوعملية': 'هو عملية',
        r'هيعملية': 'هي عملية',
        r'أساسياتالتحليل': 'أساسيات التحليل',
        r'أساسياتالتداول': 'أساسيات التداول',
        r'مفهومالتحليل': 'مفهوم التحليل',
        r'مفهومالتداول': 'مفهوم التداول',
        r'أنواعالمؤشرات': 'أنواع المؤشرات',
        r'أنواعالتحليل': 'أنواع التحليل',
        r'طرقالتحليل': 'طرق التحليل',
        r'طرقالتداول': 'طرق التداول',
        r'استراتيجياتالتداول': 'استراتيجيات التداول',
        r'استراتيجياتالتحليل': 'استراتيجيات التحليل',
        r'مؤشراتالتحليل': 'مؤشرات التحليل',
        r'مؤشراتالتداول': 'مؤشرات التداول',
        r'إدارةالمخاطر': 'إدارة المخاطر',
        r'إدارةرأسالمال': 'إدارة رأس المال',
        r'نقاطالدخول': 'نقاط الدخول',
        r'نقاطالخروج': 'نقاط الخروج',
        r'مستوياتالدعم': 'مستويات الدعم',
        r'مستوياتالمقاومة': 'مستويات المقاومة',
        r'الاتجاهالعام': 'الاتجاه العام',
        r'الاتجاهالصاعد': 'الاتجاه الصاعد',
        r'الاتجاهالهابط': 'الاتجاه الهابط',
        r'السوقالصاعد': 'السوق الصاعد',
        r'السوقالهابط': 'السوق الهابط',
        r'التحليلالفني': 'التحليل الفني',
        r'التحليلالأساسي': 'التحليل الأساسي',
        r'التداولاليومي': 'التداول اليومي',
        r'التداولالسريع': 'التداول السريع',
        r'العملاتالرقمية': 'العملات الرقمية',
        r'العملاتالمشفرة': 'العملات المشفرة',
        r'الأسواقالمالية': 'الأسواق المالية',
        r'الأدواتالمالية': 'الأدوات المالية',
        r'المنصاتالتداولية': 'المنصات التداولية',
        r'الرسومالبيانية': 'الرسوم البيانية',
        r'الشموعاليابانية': 'الشموع اليابانية',
        r'المتوسطاتالمتحركة': 'المتوسطات المتحركة',
        r'المؤشراتالفنية': 'المؤشرات الفنية',
        r'الإشاراتالتداولية': 'الإشارات التداولية',
        r'النماذجالسعرية': 'النماذج السعرية',
        r'الأنماطالفنية': 'الأنماط الفنية',
        r'القممالسعرية': 'القمم السعرية',
        r'القيعانالسعرية': 'القيعان السعرية',
        r'الكسورالسعرية': 'الكسور السعرية',
        r'الاختراقاتالسعرية': 'الاختراقات السعرية',
        r'التصحيحاتالسعرية': 'التصحيحات السعرية',
        r'الارتداداتالسعرية': 'الارتدادات السعرية',
        r'التقلباتالسعرية': 'التقلبات السعرية',
        r'الحجمالتداولي': 'الحجم التداولي',
        r'السيولةالسوقية': 'السيولة السوقية',
        r'القيمةالسوقية': 'القيمة السوقية',
        r'الرسملةالسوقية': 'الرسملة السوقية',

        # إصلاح الكلمات المدمجة مع حروف الجر
        r'فيالسوق': 'في السوق',
        r'فيالتداول': 'في التداول',
        r'فيالتحليل': 'في التحليل',
        r'علىالسعر': 'على السعر',
        r'علىالمؤشر': 'على المؤشر',
        r'علىالرسم': 'على الرسم',
        r'منالمهم': 'من المهم',
        r'منالضروري': 'من الضروري',
        r'منالأفضل': 'من الأفضل',
        r'إلىالأعلى': 'إلى الأعلى',
        r'إلىالأسفل': 'إلى الأسفل',
        r'بالنسبةللسعر': 'بالنسبة للسعر',
        r'بالنسبةللمؤشر': 'بالنسبة للمؤشر',
        r'بالنسبةللتداول': 'بالنسبة للتداول',

        # إصلاح الأرقام والوحدات
        r'(\d+)دولار': r'\1 دولار',
        r'(\d+)ريال': r'\1 ريال',
        r'(\d+)يورو': r'\1 يورو',
        r'(\d+)ساعة': r'\1 ساعة',
        r'(\d+)يوم': r'\1 يوم',
        r'(\d+)أسبوع': r'\1 أسبوع',
        r'(\d+)شهر': r'\1 شهر',
        r'(\d+)سنة': r'\1 سنة',
        r'(\d+)دقيقة': r'\1 دقيقة',
        r'(\d+)ثانية': r'\1 ثانية',
        r'(\d+)نقطة': r'\1 نقطة',
        r'(\d+)بالمئة': r'\1 بالمئة',
        r'(\d+)%': r'\1%',

        # إصلاح الكلمات مع أدوات التعريف
        r'والتحليل': 'و التحليل',
        r'والتداول': 'و التداول',
        r'والسوق': 'و السوق',
        r'والسعر': 'و السعر',
        r'والمؤشر': 'و المؤشر',
        r'والاستراتيجية': 'و الاستراتيجية',
        r'والإدارة': 'و الإدارة',
        r'والمخاطر': 'و المخاطر',

        # إصلاح العبارات الشائعة
        r'يمكنأن': 'يمكن أن',
        r'يجبأن': 'يجب أن',
        r'لابدأن': 'لا بد أن',
        r'منالممكن': 'من الممكن',
        r'منالمحتمل': 'من المحتمل',
        r'فيحالة': 'في حالة',
        r'فيحال': 'في حال',
        r'عندما': 'عندما',
        r'بحيث': 'بحيث',
        r'كماأن': 'كما أن',
        r'حيثأن': 'حيث أن',
        r'علماًأن': 'علماً أن',
        r'نظراًلأن': 'نظراً لأن',
        r'بالإضافةإلى': 'بالإضافة إلى',
        r'بالإضافةلذلك': 'بالإضافة لذلك',
        r'وبالتالي': 'و بالتالي',
        r'وبناءعلى': 'و بناء على',
        r'وبناءًعلى': 'و بناءً على',
    }

    # تطبيق الإصلاحات
    for pattern, replacement in spacing_fixes.items():
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

    # إصلاح المسافات المتعددة
    text = re.sub(r' +', ' ', text)

    # إصلاح المسافات في بداية ونهاية الأسطر
    text = re.sub(r'^ +| +$', '', text, flags=re.MULTILINE)

    return text

def simple_ai_text_cleanup(text: str, lang: str = 'ar') -> str:
    """
    تنظيف بسيط للنصوص المُولدة بالذكاء الاصطناعي بدون تعديل النجوم

    Args:
        text: النص المراد تنظيفه
        lang: لغة النص

    Returns:
        النص بعد التنظيف البسيط
    """
    if not text:
        return text

    # إزالة code blocks فقط
    text = text.strip()
    if text.startswith("```markdown"):
        text = text[len("```markdown"):].strip()
    if text.startswith("```"):
        text = text[3:].strip()
    if text.endswith("```"):
        text = text[:-3].strip()

    # إصلاح النقطتين في النص العريض فقط
    text = re.sub(r'\*\*([^*]+?):\*\*', r'**\1**:', text)
    text = re.sub(r'\*\*([^*]+?)\*\*:\*\*', r'**\1**:', text)

    # إصلاح تباعد النصوص العربية
    if lang == 'ar':
        text = fix_arabic_text_spacing(text)

    # تنظيف الأسطر الفارغة المتكررة
    text = re.sub(r'\n\n\n+', '\n\n', text)

    return text.strip()

def format_ai_analysis_content(text: str, lang: str = 'ar') -> str:
    """
    تنسيق خاص للمحتوى المُولد بالذكاء الاصطناعي مع إصلاح مشاكل التباعد

    Args:
        text: النص المراد تنسيقه
        lang: لغة النص

    Returns:
        النص بعد التنسيق والإصلاح
    """
    if not text:
        return text

    # استخدام التنظيف البسيط بدلاً من fix_bold_formatting المعقدة
    text = simple_ai_text_cleanup(text, lang)

    # تطبيق التنسيق العام
    text = clean_markdown_content(text, lang)

    # إصلاح النقطتين في النص العريض أولاً
    # مثال: **نصائح عامة:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?):\*\*', r'**\1**:', text)

    # إصلاح النقطتين المزدوجة في النص العريض
    # مثال: **نصائح عامة**:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?)\*\*:\*\*', r'**\1**:', text)

    # إزالة النجوم الزائدة وتحسين التنسيق
    # استبدال النجوم المتعددة بتنسيق bold بسيط
    text = re.sub(r'\*{3,}([^*]+)\*{3,}', r'**\1**', text)  # *** أو أكثر إلى **
    text = re.sub(r'\*{5,}', '**', text)  # إزالة النجوم الزائدة

    # إصلاح النص العريض المكسور
    text = re.sub(r'\*\*([^*\n]+)(?!\*\*)', r'**\1**', text)

    # تحويل النجوم المفردة إلى نجوم مزدوجة للنص العريض
    # تطبيق هذا بعد إصلاح النقطتين لتجنب التداخل
    # نمط محدود جداً - فقط للنصوص البسيطة مثل *كلمة* بدون تعقيدات
    # تجنب النصوص المعقدة التي تحتوي على قوائم أو نجوم متعددة
    if text.count('*') <= 4 and '\n*   ' not in text:
        text = re.sub(r'(?<!\*)\*([^*\s]+)\*(?!\*)', r'**\1**', text)

    # إضافة رموز تعبيرية متنوعة للأقسام
    if lang == 'ar':
        # استبدال العناوين البسيطة برموز تعبيرية متنوعة
        text = re.sub(r'\*\*الاتجاه العام\*\*', '📈 **الاتجاه العام**', text)
        text = re.sub(r'\*\*التحليل الفني\*\*', '🔍 **التحليل الفني**', text)
        text = re.sub(r'\*\*المؤشرات الفنية\*\*', '📊 **المؤشرات الفنية**', text)
        text = re.sub(r'\*\*مستويات الدعم والمقاومة\*\*', '🎯 **مستويات الدعم والمقاومة**', text)
        text = re.sub(r'\*\*السيناريوهات السعرية\*\*', '🔮 **السيناريوهات السعرية**', text)
        text = re.sub(r'\*\*استراتيجية التداول\*\*', '⚡ **استراتيجية التداول**', text)
        text = re.sub(r'\*\*التوصية\*\*', '💡 **التوصية**', text)
        text = re.sub(r'\*\*الخلاصة\*\*', '📝 **الخلاصة**', text)
        text = re.sub(r'\*\*إدارة المخاطر\*\*', '⚠️ **إدارة المخاطر**', text)
        text = re.sub(r'\*\*نقاط الدخول\*\*', '🚀 **نقاط الدخول**', text)
        text = re.sub(r'\*\*نقاط الخروج\*\*', '🎯 **نقاط الخروج**', text)
        text = re.sub(r'\*\*وقف الخسارة\*\*', '🛡️ **وقف الخسارة**', text)
        text = re.sub(r'\*\*هدف الربح\*\*', '💰 **هدف الربح**', text)
        text = re.sub(r'\*\*تحليل السوق\*\*', '🌐 **تحليل السوق**', text)
        text = re.sub(r'\*\*التوقعات\*\*', '🔮 **التوقعات**', text)
        text = re.sub(r'\*\*المخاطر\*\*', '⚠️ **المخاطر**', text)
        text = re.sub(r'\*\*الفرص\*\*', '✨ **الفرص**', text)

    # تحسين التباعد والتنسيق النهائي
    text = improve_spacing_and_formatting(text, lang)

    return text


def sanitize_telegram_text(text: str) -> str:
    """
    تنظيف النص لضمان توافقه مع parser تيليجرام

    Args:
        text: النص المراد تنظيفه

    Returns:
        النص المنظف والآمن للإرسال
    """
    if not text:
        return ""

    # إزالة HTML entities غير المدعومة
    import html
    text = html.unescape(text)

    # إصلاح تنسيق Markdown المعطوب
    # إصلاح النجوم المفردة أو غير المتطابقة
    import re

    # إصلاح النقطتين في النص العريض أولاً
    # مثال: **نصائح عامة:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?):\*\*', r'**\1**:', text)

    # إصلاح النقطتين المزدوجة في النص العريض
    # مثال: **نصائح عامة**:** -> **نصائح عامة**:
    text = re.sub(r'\*\*([^*]+?)\*\*:\*\*', r'**\1**:', text)

    # تحويل النجوم المفردة إلى نجوم مزدوجة للنص العريض بدلاً من إزالتها
    # هذا يحافظ على النص العريض المطلوب من المستخدم
    # نمط محسن للتعامل مع النصوص العربية والإنجليزية
    # تطبيق هذا بعد إصلاح النقطتين لتجنب التداخل
    # نمط محدود جداً - فقط للنصوص البسيطة مثل *كلمة* بدون تعقيدات
    # تجنب النصوص المعقدة التي تحتوي على قوائم أو نجوم متعددة
    if text.count('*') <= 4 and '\n*   ' not in text:
        text = re.sub(r'(?<!\*)\*([^*\s]+)\*(?!\*)', r'**\1**', text)

    # إصلاح الأقواس المربعة غير المغلقة
    text = re.sub(r'\[([^\]]*?)(?=\[|\n|$)', r'\1', text)

    # إصلاح الأقواس العادية غير المغلقة في الروابط
    text = re.sub(r'\(([^)]*?)(?=\(|\n|$)', r'(\1)', text)

    # إزالة تنسيق bold متداخل
    text = re.sub(r'\*\*([^*]*?)\*\*([^*]*?)\*\*', r'**\1\2**', text)

    # إصلاح العناوين المعطوبة
    text = re.sub(r'#{1,6}\s*([^#\n]*?)#{1,6}', r'**\1**', text)

    # إزالة الرموز الخاصة التي قد تسبب مشاكل
    problematic_chars = ['`', '~', '|', '<', '>']
    for char in problematic_chars:
        text = text.replace(char, '')

    # إصلاح المسافات المتعددة
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

    # التأكد من أن النص لا يبدأ أو ينتهي بتنسيق معطوب
    text = text.strip()

    return text


def validate_markdown_entities(text: str) -> bool:
    """
    التحقق من صحة entities في النص

    Args:
        text: النص المراد فحصه

    Returns:
        True إذا كان النص آمن، False إذا كان هناك مشاكل
    """
    import re

    # فحص الأقواس المتطابقة
    open_brackets = text.count('[')
    close_brackets = text.count(']')
    if open_brackets != close_brackets:
        return False

    # فحص الأقواس العادية
    open_parens = text.count('(')
    close_parens = text.count(')')
    if open_parens != close_parens:
        return False

    # فحص النجوم المتطابقة للـ bold
    bold_count = len(re.findall(r'\*\*', text))
    if bold_count % 2 != 0:
        return False

    # فحص النجوم المفردة للـ italic
    # بعد التحديث، جميع النجوم المفردة يتم تحويلها إلى نجوم مزدوجة
    # لذلك نتحقق فقط من وجود نجوم مفردة متبقية (يجب ألا تكون موجودة)
    remaining_single_stars = len(re.findall(r'(?<!\*)\*(?!\*)', text))
    if remaining_single_stars % 2 != 0:
        return False

    return True


def split_long_message(text: str, max_length: int = 3800) -> list:
    """
    تقسيم الرسائل الطويلة إلى أجزاء متعددة مع الحفاظ على التنسيق

    Args:
        text: النص المراد تقسيمه
        max_length: الحد الأقصى لطول كل جزء

    Returns:
        قائمة بأجزاء النص
    """
    if len(text) <= max_length:
        return [text]

    parts = []
    current_part = ""

    # تقسيم النص إلى أسطر
    lines = text.split('\n')

    for line in lines:
        # إذا كان السطر الواحد أطول من الحد الأقصى، قسمه
        if len(line) > max_length:
            # إذا كان هناك جزء حالي، أضفه إلى القائمة
            if current_part:
                parts.append(current_part.strip())
                current_part = ""

            # تقسيم السطر الطويل إلى أجزاء
            words = line.split(' ')
            temp_line = ""

            for word in words:
                if len(temp_line + " " + word) <= max_length:
                    temp_line += (" " + word) if temp_line else word
                else:
                    if temp_line:
                        parts.append(temp_line.strip())
                    temp_line = word

            if temp_line:
                current_part = temp_line
        else:
            # إذا كان إضافة السطر سيتجاوز الحد الأقصى
            if len(current_part + "\n" + line) > max_length:
                if current_part:
                    parts.append(current_part.strip())
                current_part = line
            else:
                current_part += ("\n" + line) if current_part else line

    # إضافة الجزء الأخير
    if current_part:
        parts.append(current_part.strip())

    return parts


import asyncio


async def delete_message_after_delay(bot, chat_id, message_id, delay_seconds):
    """حذف رسالة بعد فترة زمنية محددة"""
    try:
        await asyncio.sleep(delay_seconds)
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        logger.info(f"تم حذف الرسالة {message_id} بعد {delay_seconds} ثانية")
    except Exception as e:
        logger.error(f"خطأ في حذف الرسالة: {str(e)}")
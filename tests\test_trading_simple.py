#!/usr/bin/env python3
"""
Simple Trading System Test
==========================

A simple test to verify that all trading system components work correctly
without needing to run the full bot.

Author: Augment Agent
"""

import sys
import os
import asyncio
import logging

# Add src folder to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_trading_system():
    """Test the automated trading system"""
    
    print("Starting Trading System Test...")
    print("=" * 50)
    
    # Test 1: Import modules
    print("\nTesting module imports...")
    try:
        from trading.trading_db import TradingDatabaseManager, TradingRecommendation
        from trading.risk_manager import RiskManager, RiskLevel, ComplianceStatus
        from trading.market_analyzer import <PERSON>Analyzer, MarketSignal
        from trading.recommendation_engine import RecommendationEngine
        from trading.auto_trading_system import AutoTradingSystem, TradingPhase
        from handlers.trading_handlers import TradingHandlers
        
        print("SUCCESS: All trading modules imported successfully")
    except Exception as e:
        print(f"ERROR: Failed to import modules: {e}")
        return False
    
    # Test 2: Create system objects (without database)
    print("\nTesting system object creation...")
    try:
        # Create risk manager
        risk_manager = RiskManager()
        print("SUCCESS: Risk manager created")
        
        # Test Islamic compliance filtering
        test_symbols = ["BTC", "ETH", "BNB", "DOGE", "SHIB"]
        compliant_symbols = []
        
        for symbol in test_symbols:
            compliance = risk_manager.compliance_filter.check_symbol_compliance(symbol)
            if compliance == ComplianceStatus.COMPLIANT:
                compliant_symbols.append(symbol)
        
        print(f"SUCCESS: Sharia-compliant symbols: {compliant_symbols}")
        
    except Exception as e:
        print(f"ERROR: Failed to create system objects: {e}")
        return False
    
    # Test 3: Test analysis logic
    print("\nTesting analysis logic...")
    try:
        # Mock data for testing
        test_data = {
            'prices': [50000, 51000, 50500, 52000, 51500],
            'volumes': [1000, 1200, 900, 1500, 1100]
        }
        
        # Simple RSI calculation
        def calculate_simple_rsi(prices, period=4):
            if len(prices) < period + 1:
                return 50  # Default value
            
            gains = []
            losses = []
            
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        rsi = calculate_simple_rsi(test_data['prices'])
        print(f"SUCCESS: Calculated RSI: {rsi:.2f}")
        
        # Determine trading signal
        if rsi < 30:
            signal = "Strong Buy"
        elif rsi < 50:
            signal = "Buy"
        elif rsi > 70:
            signal = "Strong Sell"
        elif rsi > 50:
            signal = "Sell"
        else:
            signal = "Hold"
        
        print(f"SUCCESS: Trading signal: {signal}")
        
    except Exception as e:
        print(f"ERROR: Failed analysis logic test: {e}")
        return False
    
    # Test 4: Test trading phases
    print("\nTesting trading phases...")
    try:
        phases = [
            TradingPhase.PHASE_1_RECOMMENDATIONS,
            TradingPhase.PHASE_2_LIMITED_AUTO,
            TradingPhase.PHASE_3_FULL_AUTO
        ]
        
        for phase in phases:
            print(f"SUCCESS: Phase available: {phase.value}")
        
    except Exception as e:
        print(f"ERROR: Failed trading phases test: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("SUCCESS: All tests passed!")
    print("SUCCESS: Automated trading system is ready for deployment")
    return True

def main():
    """Main function"""
    try:
        # Run tests
        result = asyncio.run(test_trading_system())
        
        if result:
            print("\nSUCCESS: System ready for deployment to hosting!")
            sys.exit(0)
        else:
            print("\nERROR: Errors must be fixed before deployment")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nINFO: Test stopped by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nERROR: Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

"""
🔧 نظام محسن لجلب بيانات السوق
===============================

نظام محسن للحصول على بيانات السوق مع آليات متقدمة للتعامل مع:
- Rate limiting
- مصادر بيانات متعددة
- التخزين المؤقت
- إعادة المحاولة التلقائية

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MarketDataSource:
    """مصدر بيانات السوق"""
    name: str
    priority: int
    rate_limit: float  # ثواني بين الطلبات
    timeout: int
    max_retries: int

class EnhancedMarketDataManager:
    """مدير محسن لبيانات السوق"""
    
    def __init__(self):
        self.cache = {}
        self.last_request_times = {}
        
        # تكوين مصادر البيانات
        self.data_sources = [
            MarketDataSource("coingecko", 1, 1.0, 15, 3),
            MarketDataSource("binance", 2, 0.2, 10, 2),
            MarketDataSource("cache", 3, 0.0, 1, 1)
        ]
        
        # خريطة رموز العملات لـ CoinGecko
        self.coingecko_mapping = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum', 
            'BNB': 'binancecoin',
            'ADA': 'cardano',
            'DOT': 'polkadot',
            'LINK': 'chainlink',
            'LTC': 'litecoin',
            'BCH': 'bitcoin-cash',
            'XLM': 'stellar',
            'VET': 'vechain',
            'ALGO': 'algorand',
            'ATOM': 'cosmos',
            'SOL': 'solana',
            'AVAX': 'avalanche-2',
            'MATIC': 'matic-network',
            'FTM': 'fantom',
            'NEAR': 'near',
            'ICP': 'internet-computer'
        }
    
    async def get_market_data(self, symbol: str, timeframe: str = '1d', user_id: str = None,
                             analysis_type: str = 'basic') -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات السوق مع آليات تحسين متقدمة ودعم أنواع التحليل المختلفة

        Args:
            symbol: رمز العملة
            timeframe: الإطار الزمني
            user_id: معرف المستخدم (للتحقق من الصلاحيات)
            analysis_type: نوع التحليل (basic, enhanced, ai)

        Returns:
            بيانات السوق أو None
        """
        logger.info(f"جاري الحصول على بيانات السوق لـ {symbol} - نوع التحليل: {analysis_type}")

        # التحقق من صلاحيات المستخدم إذا تم تمرير user_id
        if user_id:
            access_granted = await self._check_user_access(user_id, analysis_type)
            if not access_granted:
                logger.warning(f"المستخدم {user_id} لا يملك صلاحية للتحليل {analysis_type}")
                return None

        # ترتيب مصادر البيانات حسب الأولوية ونوع التحليل
        sorted_sources = await self._get_prioritized_sources(analysis_type)

        for source in sorted_sources:
            try:
                # التحقق من rate limiting
                if not await self._check_rate_limit(source.name, source.rate_limit):
                    logger.warning(f"تجاوز حد المعدل لـ {source.name}")
                    continue

                # محاولة الحصول على البيانات
                data = await self._get_data_from_source(source, symbol, timeframe)

                if data:
                    logger.info(f"تم الحصول على البيانات من {source.name} لـ {symbol}")

                    # إضافة معلومات التحليل
                    data['analysis_type'] = analysis_type
                    data['user_id'] = user_id

                    # حفظ في التخزين المؤقت
                    await self._cache_data(symbol, data)

                    return data

            except Exception as e:
                logger.warning(f"فشل مصدر {source.name} لـ {symbol}: {str(e)}")
                continue

        logger.error(f"فشل في الحصول على بيانات السوق لـ {symbol} من جميع المصادر")
        return None

    async def _check_user_access(self, user_id: str, analysis_type: str) -> bool:
        """
        التحقق من صلاحيات المستخدم لنوع التحليل المطلوب

        Args:
            user_id: معرف المستخدم
            analysis_type: نوع التحليل (basic, enhanced, ai)

        Returns:
            True إذا كان المستخدم يملك الصلاحية
        """
        try:
            # استيراد نظام الاشتراك
            from services.subscription_system import subscription_system
            from api_manager import api_manager

            # التحقق من حالة الاشتراك
            subscription = await subscription_system.is_subscribed(user_id, full_details=True)
            is_subscribed = subscription.get('is_active', False) if isinstance(subscription, dict) else subscription
            is_free_day = subscription.get('is_free_day', False) if isinstance(subscription, dict) else False

            # التحليل الأساسي متاح للجميع
            if analysis_type == 'basic':
                return True

            # التحليل المحسن يتطلب اشتراك أو يوم مجاني
            elif analysis_type == 'enhanced':
                return is_subscribed or is_free_day

            # تحليل الذكاء الاصطناعي يتطلب اشتراك + API key
            elif analysis_type == 'ai':
                if not (is_subscribed or is_free_day):
                    return False

                # التحقق من وجود مفتاح API للذكاء الاصطناعي
                has_api_key = await api_manager.has_api_keys(user_id, 'gemini')
                return has_api_key

            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من صلاحيات المستخدم {user_id}: {str(e)}")
            return analysis_type == 'basic'  # السماح بالتحليل الأساسي في حالة الخطأ

    async def _get_prioritized_sources(self, analysis_type: str) -> List[MarketDataSource]:
        """
        ترتيب مصادر البيانات حسب نوع التحليل

        Args:
            analysis_type: نوع التحليل

        Returns:
            قائمة مرتبة من مصادر البيانات
        """
        sources = self.data_sources.copy()

        # للتحليل المحسن والذكاء الاصطناعي، إعطاء أولوية أعلى لمصادر أكثر دقة
        if analysis_type in ['enhanced', 'ai']:
            # ترتيب خاص للتحليل المتقدم
            priority_map = {
                'coingecko': 1,  # أولوية عالية للبيانات الشاملة
                'binance': 2,    # أولوية متوسطة للبيانات السريعة
                'cache': 3       # أولوية منخفضة للبيانات المخزنة
            }
        else:
            # ترتيب عادي للتحليل الأساسي
            priority_map = {
                'cache': 1,      # أولوية عالية للسرعة
                'binance': 2,    # أولوية متوسطة
                'coingecko': 3   # أولوية منخفضة لتوفير الطلبات
            }

        # تحديث الأولويات
        for source in sources:
            if source.name in priority_map:
                source.priority = priority_map[source.name]

        return sorted(sources, key=lambda x: x.priority)
    
    async def _check_rate_limit(self, source_name: str, rate_limit: float) -> bool:
        """التحقق من حدود المعدل"""
        if rate_limit == 0:
            return True
            
        current_time = datetime.now()
        last_request = self.last_request_times.get(source_name)
        
        if last_request:
            time_diff = (current_time - last_request).total_seconds()
            if time_diff < rate_limit:
                return False
        
        self.last_request_times[source_name] = current_time
        return True
    
    async def _get_data_from_source(self, source: MarketDataSource, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات من مصدر محدد"""
        
        if source.name == "coingecko":
            return await self._get_coingecko_data(symbol, source)
        elif source.name == "binance":
            return await self._get_binance_data(symbol, source)
        elif source.name == "cache":
            return await self._get_cached_data(symbol)
        
        return None
    
    async def _get_coingecko_data(self, symbol: str, source: MarketDataSource) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات من CoinGecko"""
        try:
            coin_id = self.coingecko_mapping.get(symbol.upper(), symbol.lower())
            
            url = f"https://api.coingecko.com/api/v3/coins/{coin_id}"
            params = {
                'localization': 'false',
                'tickers': 'false',
                'market_data': 'true',
                'community_data': 'false',
                'developer_data': 'false',
                'sparkline': 'false'
            }
            
            timeout = aiohttp.ClientTimeout(total=source.timeout)
            
            for attempt in range(source.max_retries):
                try:
                    async with aiohttp.ClientSession(timeout=timeout) as session:
                        async with session.get(url, params=params) as response:
                            
                            if response.status == 200:
                                data = await response.json()
                                
                                return {
                                    'symbol': symbol,
                                    'current_price': data['market_data']['current_price']['usd'],
                                    'market_cap': data['market_data']['market_cap']['usd'],
                                    'total_volume': data['market_data']['total_volume']['usd'],
                                    'price_change_24h': data['market_data']['price_change_percentage_24h'],
                                    'price_change_7d': data['market_data']['price_change_percentage_7d'],
                                    'market_cap_rank': data['market_data']['market_cap_rank'],
                                    'high_24h': data['market_data']['high_24h']['usd'],
                                    'low_24h': data['market_data']['low_24h']['usd'],
                                    'timestamp': datetime.now(),
                                    'source': 'coingecko'
                                }
                            
                            elif response.status == 429:
                                logger.warning(f"Rate limit CoinGecko - محاولة {attempt + 1}")
                                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                                continue
                            
                            elif response.status == 404:
                                logger.warning(f"العملة {symbol} غير موجودة في CoinGecko")
                                return None
                            
                            else:
                                logger.warning(f"CoinGecko error {response.status} - محاولة {attempt + 1}")
                                await asyncio.sleep(1)
                                continue
                
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout CoinGecko - محاولة {attempt + 1}")
                    await asyncio.sleep(1)
                    continue
                except Exception as e:
                    logger.warning(f"خطأ CoinGecko - محاولة {attempt + 1}: {str(e)}")
                    await asyncio.sleep(1)
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في CoinGecko API لـ {symbol}: {str(e)}")
            return None
    
    async def _get_binance_data(self, symbol: str, source: MarketDataSource) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات من Binance"""
        try:
            binance_symbol = f"{symbol.upper()}USDT"
            
            url = "https://api.binance.com/api/v3/ticker/24hr"
            params = {'symbol': binance_symbol}
            
            timeout = aiohttp.ClientTimeout(total=source.timeout)
            
            for attempt in range(source.max_retries):
                try:
                    async with aiohttp.ClientSession(timeout=timeout) as session:
                        async with session.get(url, params=params) as response:
                            
                            if response.status == 200:
                                data = await response.json()
                                
                                return {
                                    'symbol': symbol,
                                    'current_price': float(data['lastPrice']),
                                    'price_change_24h': float(data['priceChangePercent']),
                                    'high_24h': float(data['highPrice']),
                                    'low_24h': float(data['lowPrice']),
                                    'total_volume': float(data['volume']),
                                    'timestamp': datetime.now(),
                                    'source': 'binance'
                                }
                            
                            elif response.status == 400:
                                logger.warning(f"رمز غير صحيح في Binance: {binance_symbol}")
                                return None
                            
                            else:
                                logger.warning(f"Binance error {response.status} - محاولة {attempt + 1}")
                                await asyncio.sleep(0.5)
                                continue
                
                except Exception as e:
                    logger.warning(f"خطأ Binance - محاولة {attempt + 1}: {str(e)}")
                    await asyncio.sleep(0.5)
                    continue
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في Binance API لـ {symbol}: {str(e)}")
            return None
    
    async def _get_cached_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات من التخزين المؤقت"""
        try:
            cache_key = f"market_{symbol}"
            
            if cache_key in self.cache:
                cached_data = self.cache[cache_key]
                cache_age = (datetime.now() - cached_data['timestamp']).total_seconds()
                
                # استخدام البيانات المؤقتة إذا كانت أحدث من 5 دقائق
                if cache_age < 300:
                    logger.info(f"استخدام البيانات المؤقتة لـ {symbol}")
                    return cached_data
            
            return None
            
        except Exception as e:
            logger.warning(f"خطأ في التخزين المؤقت لـ {symbol}: {str(e)}")
            return None
    
    async def _cache_data(self, symbol: str, data: Dict[str, Any]):
        """حفظ البيانات في التخزين المؤقت"""
        try:
            cache_key = f"market_{symbol}"
            self.cache[cache_key] = data
            
            # تنظيف التخزين المؤقت القديم
            await self._cleanup_cache()
            
        except Exception as e:
            logger.warning(f"خطأ في حفظ التخزين المؤقت: {str(e)}")
    
    async def _cleanup_cache(self):
        """تنظيف التخزين المؤقت القديم"""
        try:
            current_time = datetime.now()
            keys_to_remove = []
            
            for key, data in self.cache.items():
                if (current_time - data['timestamp']).total_seconds() > 1800:  # 30 دقيقة
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.cache[key]
                
            if keys_to_remove:
                logger.info(f"تم تنظيف {len(keys_to_remove)} عنصر من التخزين المؤقت")
                
        except Exception as e:
            logger.warning(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")

# إنشاء مثيل عام
enhanced_market_data = EnhancedMarketDataManager()

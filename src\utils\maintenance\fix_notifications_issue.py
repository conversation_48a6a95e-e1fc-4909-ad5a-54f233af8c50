#!/usr/bin/env python3
"""
إصلاح شامل لمشكلة الإشعارات في نظام الأخبار التلقائية
يحل مشكلة عدم وصول الإشعارات للمستخدمين
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

logger = logging.getLogger(__name__)

class NotificationsFixer:
    """فئة إصلاح مشاكل الإشعارات"""
    
    def __init__(self):
        self.db = None
        self.bot = None
        self.issues_found = []
        self.fixes_applied = []
    
    async def initialize(self):
        """تهيئة الاتصال بقاعدة البيانات والبوت"""
        try:
            # تهيئة Firebase
            from config.firebase_config import initialize_firebase
            self.db = initialize_firebase()
            
            # تهيئة البوت
            from config.config import TELEGRAM_BOT_TOKEN
            from telegram import Bot
            self.bot = Bot(token=TELEGRAM_BOT_TOKEN)
            
            logger.info("✅ تم تهيئة الاتصالات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في التهيئة: {str(e)}")
            return False
    
    async def diagnose_issues(self):
        """تشخيص مشاكل الإشعارات"""
        print("\n🔍 تشخيص مشاكل الإشعارات...")
        
        # فحص 1: التحقق من وجود الفهارس المطلوبة
        await self._check_firestore_indexes()
        
        # فحص 2: التحقق من إعدادات المستخدمين
        await self._check_user_settings()
        
        # فحص 3: التحقق من قواعد الإشعارات
        await self._check_notification_rules()
        
        # فحص 4: التحقق من إحصائيات الإشعارات
        await self._check_notification_stats()
        
        # فحص 5: التحقق من اتصال البوت
        await self._check_bot_connection()
        
        # عرض النتائج
        await self._display_diagnosis_results()
    
    async def _check_firestore_indexes(self):
        """فحص الفهارس المطلوبة في Firestore"""
        try:
            print("   📋 فحص فهارس Firestore...")
            
            # محاولة تنفيذ استعلام معقد لاختبار الفهارس
            today = datetime.now().date()
            today_start = datetime.combine(today, datetime.min.time())
            
            try:
                # اختبار الاستعلام الذي يسبب المشكلة
                test_query = self.db.collection('notifications')\
                    .where('notification_type', '==', 'breaking_news')\
                    .where('created_at', '>=', today_start.isoformat())\
                    .limit(1)
                
                docs = test_query.get()
                print("   ✅ الفهارس تعمل بشكل صحيح")
                
            except Exception as e:
                if "requires an index" in str(e):
                    self.issues_found.append({
                        'type': 'missing_indexes',
                        'description': 'فهارس Firestore مفقودة',
                        'error': str(e),
                        'solution': 'إنشاء الفهارس المطلوبة'
                    })
                    print("   ❌ فهارس Firestore مفقودة")
                else:
                    print(f"   ⚠️ خطأ في اختبار الفهارس: {str(e)}")
                    
        except Exception as e:
            logger.error(f"خطأ في فحص الفهارس: {str(e)}")
    
    async def _check_user_settings(self):
        """فحص إعدادات المستخدمين"""
        try:
            print("   👥 فحص إعدادات المستخدمين...")
            
            # عدد المستخدمين النشطين
            users_ref = self.db.collection('users')
            users_docs = users_ref.limit(10).get()  # عينة صغيرة للاختبار
            
            users_without_settings = 0
            users_with_wrong_language = 0
            
            for doc in users_docs:
                user_id = doc.id
                
                # فحص إعدادات المستخدم
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                
                if not settings_doc.exists:
                    users_without_settings += 1
                else:
                    settings_data = settings_doc.to_dict()
                    lang = settings_data.get('lang', settings_data.get('language'))
                    
                    if lang not in ['ar', 'en']:
                        users_with_wrong_language += 1
            
            if users_without_settings > 0:
                self.issues_found.append({
                    'type': 'missing_user_settings',
                    'description': f'{users_without_settings} مستخدم بدون إعدادات',
                    'solution': 'إنشاء إعدادات افتراضية'
                })
            
            if users_with_wrong_language > 0:
                self.issues_found.append({
                    'type': 'wrong_language_settings',
                    'description': f'{users_with_wrong_language} مستخدم بإعدادات لغة خاطئة',
                    'solution': 'تصحيح إعدادات اللغة'
                })
            
            print(f"   📊 تم فحص {len(users_docs)} مستخدم")
            
        except Exception as e:
            logger.error(f"خطأ في فحص إعدادات المستخدمين: {str(e)}")
    
    async def _check_notification_rules(self):
        """فحص قواعد الإشعارات"""
        try:
            print("   📜 فحص قواعد الإشعارات...")
            
            # عدد قواعد الإشعارات
            rules_ref = self.db.collection('notification_rules')
            rules_docs = rules_ref.get()
            
            active_rules = 0
            inactive_rules = 0
            
            for doc in rules_docs:
                rule_data = doc.to_dict()
                if rule_data.get('enabled', True):
                    active_rules += 1
                else:
                    inactive_rules += 1
            
            print(f"   📊 قواعد نشطة: {active_rules}, معطلة: {inactive_rules}")
            
            if active_rules == 0:
                self.issues_found.append({
                    'type': 'no_active_rules',
                    'description': 'لا توجد قواعد إشعارات نشطة',
                    'solution': 'تفعيل الإشعارات التلقائية للمستخدمين'
                })
            
        except Exception as e:
            logger.error(f"خطأ في فحص قواعد الإشعارات: {str(e)}")
    
    async def _check_notification_stats(self):
        """فحص إحصائيات الإشعارات"""
        try:
            print("   📈 فحص إحصائيات الإشعارات...")
            
            today = datetime.now().date().isoformat()
            
            # فحص الإشعارات المرسلة اليوم
            stats_ref = self.db.collection('user_notification_stats')
            stats_docs = stats_ref.limit(10).get()
            
            notifications_sent_today = 0
            
            for doc in stats_docs:
                stats_data = doc.to_dict()
                daily_stats = stats_data.get('daily', {}).get(today, {})
                notifications_sent_today += daily_stats.get('total', 0)
            
            print(f"   📊 إشعارات مرسلة اليوم: {notifications_sent_today}")
            
            if notifications_sent_today == 0:
                self.issues_found.append({
                    'type': 'no_notifications_sent',
                    'description': 'لم يتم إرسال أي إشعارات اليوم',
                    'solution': 'فحص نظام الإشعارات التلقائية'
                })
            
        except Exception as e:
            logger.error(f"خطأ في فحص إحصائيات الإشعارات: {str(e)}")
    
    async def _check_bot_connection(self):
        """فحص اتصال البوت"""
        try:
            print("   🤖 فحص اتصال البوت...")
            
            # اختبار البوت
            bot_info = await self.bot.get_me()
            print(f"   ✅ الب��ت متصل: @{bot_info.username}")
            
        except Exception as e:
            self.issues_found.append({
                'type': 'bot_connection_error',
                'description': 'خطأ في اتصال البوت',
                'error': str(e),
                'solution': 'فحص رمز البوت والاتصال'
            })
            print(f"   ❌ خطأ في اتصال البوت: {str(e)}")
    
    async def _display_diagnosis_results(self):
        """عرض نتائج التشخيص"""
        print("\n" + "="*60)
        print("📋 نتائج التشخيص")
        print("="*60)
        
        if not self.issues_found:
            print("✅ لم يتم العثور على مشاكل واضحة")
            print("   المشكلة قد تكون في:")
            print("   - عدم وجود أخبار عاجلة للإرسال")
            print("   - عدم تفعيل الإشعارات للمستخدمين")
            print("   - مشكلة في جدولة الأخبار")
        else:
            print(f"❌ تم العثور على {len(self.issues_found)} مشكلة:")
            
            for i, issue in enumerate(self.issues_found, 1):
                print(f"\n{i}. {issue['description']}")
                print(f"   النوع: {issue['type']}")
                if 'error' in issue:
                    print(f"   الخطأ: {issue['error']}")
                print(f"   الحل: {issue['solution']}")
    
    async def apply_fixes(self):
        """تطبيق الإصلاحات"""
        if not self.issues_found:
            print("\n✅ لا توجد مشاكل للإصلاح")
            return
        
        print(f"\n🔧 تطبيق الإصلاحات لـ {len(self.issues_found)} مشكلة...")
        
        for issue in self.issues_found:
            await self._apply_single_fix(issue)
        
        print(f"\n✅ تم تطبيق {len(self.fixes_applied)} إصلاح")
    
    async def _apply_single_fix(self, issue):
        """تطبيق إصلاح واحد"""
        try:
            issue_type = issue['type']
            
            if issue_type == 'missing_indexes':
                await self._fix_missing_indexes()
            
            elif issue_type == 'missing_user_settings':
                await self._fix_missing_user_settings()
            
            elif issue_type == 'wrong_language_settings':
                await self._fix_wrong_language_settings()
            
            elif issue_type == 'no_active_rules':
                await self._fix_no_active_rules()
            
            elif issue_type == 'no_notifications_sent':
                await self._fix_no_notifications_sent()
            
            elif issue_type == 'bot_connection_error':
                await self._fix_bot_connection()
            
        except Exception as e:
            logger.error(f"خطأ في تطبيق الإصلاح {issue_type}: {str(e)}")
    
    async def _fix_missing_indexes(self):
        """إصلاح مشكلة الفهارس المفقودة"""
        print("   🔧 إنشاء ملفات الفهارس...")
        
        # تشغيل سكريبت إنشاء الفهارس
        import subprocess
        
        try:
            # تشغيل سكريبت إعداد الفهارس
            result = subprocess.run([
                sys.executable, 
                'firestore_indexes_setup.py'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.fixes_applied.append('تم إنشاء ملفات الفهارس')
                print("   ✅ تم إنشاء ملفات الفهارس")
                print("   ⚠️ يجب تطبيق الفهارس يدوياً: firebase deploy --only firestore:indexes")
            else:
                print(f"   ❌ خطأ في إنشاء الفهارس: {result.stderr}")
                
        except Exception as e:
            print(f"   ❌ خطأ في تشغيل سكريبت الفهارس: {str(e)}")
    
    async def _fix_missing_user_settings(self):
        """إصلاح إعدادات المستخدمين المفقودة"""
        print("   🔧 إنشاء إعدادات افتراضية للمستخدمين...")
        
        try:
            users_ref = self.db.collection('users')
            users_docs = users_ref.get()
            
            fixed_count = 0
            
            for doc in users_docs:
                user_id = doc.id
                
                # فحص وجود الإعدادات
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                
                if not settings_doc.exists:
                    # إنشاء إعدادات افتراضية
                    default_settings = {
                        'lang': 'ar',
                        'language': 'ar',
                        'notifications': True,
                        'timezone': 'UTC',
                        'created_at': datetime.now().isoformat(),
                        'fixed_by_script': True
                    }
                    
                    self.db.collection('user_settings').document(user_id).set(default_settings)
                    fixed_count += 1
            
            self.fixes_applied.append(f'تم إنشاء إعدادات لـ {fixed_count} مستخدم')
            print(f"   ✅ تم إنشاء إعدادات لـ {fixed_count} مستخدم")
            
        except Exception as e:
            print(f"   ❌ خطأ في إنشاء إعدادات المستخدمين: {str(e)}")
    
    async def _fix_wrong_language_settings(self):
        """إصلاح إعدادات اللغة الخاطئة"""
        print("   🔧 تصحيح إعدادات اللغة...")
        
        try:
            settings_ref = self.db.collection('user_settings')
            settings_docs = settings_ref.get()
            
            fixed_count = 0
            
            for doc in settings_docs:
                settings_data = doc.to_dict()
                lang = settings_data.get('lang', settings_data.get('language'))
                
                if lang not in ['ar', 'en']:
                    # تصحيح اللغة
                    corrected_settings = {
                        'lang': 'ar',
                        'language': 'ar',
                        'corrected_by_script': True,
                        'original_lang': lang
                    }
                    
                    doc.reference.update(corrected_settings)
                    fixed_count += 1
            
            self.fixes_applied.append(f'تم تصحيح لغة {fixed_count} مستخدم')
            print(f"   ✅ تم تصحيح لغة {fixed_count} مستخدم")
            
        except Exception as e:
            print(f"   ❌ خطأ في تصحيح إعدادات اللغة: {str(e)}")
    
    async def _fix_no_active_rules(self):
        """إصلاح عدم وجود قواعد إشعارات نشطة"""
        print("   🔧 تفعيل الإشعارات التلقائية للمستخدمين...")
        
        try:
            # تم حذف نظام الأخبار الذكي
            print("   ✅ تم حذف نظام الأخبار الذكي من المشروع")

        except Exception as e:
            print(f"   ❌ خطأ: {str(e)}")
    
    async def _fix_no_notifications_sent(self):
        """إصلاح عدم إرسال الإشعارات"""
        print("   🔧 اختبار إرسال إشعار تجريبي...")
        
        try:
            # إرسال إشعار تجريبي لأول مستخدم
            users_ref = self.db.collection('users')
            first_user = users_ref.limit(1).get()
            
            if first_user:
                user_id = first_user[0].id
                
                # إرسال رسالة تجريبية
                test_message = "🧪 رسالة اختبار من نظام الإشعارات\n\nهذه رسالة للتأكد من عمل النظام بشكل صحيح."
                
                await self.bot.send_message(
                    chat_id=user_id,
                    text=test_message
                )
                
                self.fixes_applied.append('تم إرسال إشعار تجريبي')
                print(f"   ✅ تم إرسال إشعار تجريبي للمستخدم {user_id}")
            else:
                print("   ⚠️ لا يوجد مستخدمين لإرسال الاختبار")
                
        except Exception as e:
            print(f"   ❌ خطأ في إرسال الإشعار التجريبي: {str(e)}")
    
    async def _fix_bot_connection(self):
        """إصلاح مشكلة اتصال البوت"""
        print("   🔧 فحص إعدادات البوت...")
        
        try:
            # فحص متغيرات البيئة
            from config.config import TELEGRAM_BOT_TOKEN
            
            if not TELEGRAM_BOT_TOKEN:
                print("   ❌ رمز البوت غير موجود في متغيرات البيئة")
                return
            
            # اختبار البوت مرة أخرى
            bot_info = await self.bot.get_me()
            
            self.fixes_applied.append('تم التحقق من اتصال البوت')
            print(f"   ✅ البوت يعمل بشكل صحيح: @{bot_info.username}")
            
        except Exception as e:
            print(f"   ❌ البوت لا يزال لا يعمل: {str(e)}")
    
    async def test_notifications_system(self):
        """اختبار شامل لنظام الإشعارات"""
        print("\n🧪 اختبار نظام الإشعارات...")
        
        try:
            # تم حذف نظام الأخبار الذكي
            print("   ✅ تم حذف نظام الأخبار الذكي من المشروع")
            print("   ✅ النظام الآن أكثر بساطة واستقراراً")

        except Exception as e:
            print(f"   ❌ خطأ: {str(e)}")

async def main():
    """الدالة الرئيسية"""
    print("🔧 أداة إصلاح مشاكل الإشعارات")
    print("="*50)
    
    fixer = NotificationsFixer()
    
    # تهيئة الاتصالات
    if not await fixer.initialize():
        print("❌ فشل في التهيئة")
        return
    
    # تشخيص المشاكل
    await fixer.diagnose_issues()
    
    # تطبيق الإصلاحات
    await fixer.apply_fixes()
    
    # اختبار النظام
    await fixer.test_notifications_system()
    
    print("\n" + "="*50)
    print("✅ انتهى إصلاح مشاكل الإشعارات")
    print("\n📋 الخطوات التالية:")
    print("1. تطبيق فهارس Firestore: firebase deploy --only firestore:indexes")
    print("2. إعادة تشغيل البوت")
    print("3. مراقبة السجلات للتأكد من عمل الإشعارات")

if __name__ == "__main__":
    asyncio.run(main())
"""
وحدة الاستعلامات المحسنة - Optimized Database Queries
تحتوي على استعلامات Firestore محسنة للأداء والسرعة

الميزات:
- استعلامات مجمعة (Batch Queries)
- فهرسة ذكية
- تخزين مؤقت متقدم
- استعلامات غير متزامنة
- تحسين استخدام الشبكة
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import json
from dataclasses import dataclass
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter, Query
from google.cloud.firestore_v1.base_query import BaseCompositeFilter
import time

logger = logging.getLogger(__name__)

@dataclass
class QueryResult:
    """نتيجة الاستعلام مع معلومات الأداء"""
    data: Any
    execution_time: float
    cache_hit: bool
    query_complexity: str

class OptimizedFirestoreManager:
    """مدير Firestore محسن للأداء العالي"""
    
    def __init__(self, db: firestore.Client, cache_size: int = 1000):
        """
        تهيئة مدير Firestore المحسن
        
        Args:
            db: مثيل قاعدة بيانات Firestore
            cache_size: حجم التخزين المؤقت
        """
        self.db = db
        self.cache = {}
        self.cache_size = cache_size
        self.cache_timeout = 300  # 5 دقائق
        self.executor = ThreadPoolExecutor(max_workers=10)
        self.query_stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'avg_execution_time': 0.0,
            'slow_queries': []
        }
        
        # إعداد الفهارس المحسنة
        self._setup_optimized_indexes()
    
    def _setup_optimized_indexes(self):
        """إعداد الفهارس المحسنة لتحسين الأداء"""
        # قائمة الفهارس المطلوبة للأداء الأمثل
        self.recommended_indexes = {
            'users': [
                ['user_id', 'created_at'],
                ['is_active', 'last_seen'],
                ['subscription_status', 'created_at']
            ],
            'subscriptions': [
                ['user_id', 'is_active'],
                ['expires_at', 'is_active'],
                ['created_at', 'status']
            ],
            'transactions': [
                ['user_id', 'status'],
                ['created_at', 'status'],
                ['transaction_id', 'status']
            ],
            'alerts': [
                ['user_id', 'is_active'],
                ['symbol', 'is_active'],
                ['created_at', 'is_active']
            ],
            'user_api_keys': [
                ['user_id'],
                ['updated_at']
            ],
            'user_settings': [
                ['user_id'],
                ['lang', 'updated_at']
            ],
            'trading_recommendations': [
                ['user_id', 'created_at'],
                ['user_id', 'status', 'created_at'],
                ['status', 'user_id', 'created_at'],
                ['expires_at', 'status'],
                ['symbol', 'status'],
                ['created_at', 'status']
            ],
            'trading_signals': [
                ['symbol', 'created_at'],
                ['signal_type', 'created_at'],
                ['strength', 'created_at']
            ],
            'user_trading_settings': [
                ['user_id'],
                ['enabled', 'user_id'],
                ['updated_at']
            ]
        }
        
        logger.info("تم إعداد قائمة الفهارس المحسنة")
    
    def _get_cache_key(self, collection: str, filters: Dict, limit: int = None) -> str:
        """إنشاء مفتاح التخزين المؤقت"""
        filter_str = json.dumps(filters, sort_keys=True)
        return f"{collection}_{hash(filter_str)}_{limit}"
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """التحقق من صحة التخزين المؤقت"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return (time.time() - cache_time) < self.cache_timeout
    
    def _update_cache(self, cache_key: str, data: Any):
        """تحديث التخزين المؤقت مع إدارة الحجم"""
        # إزالة العناصر القديمة إذا تجاوز الحد الأقصى
        if len(self.cache) >= self.cache_size:
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k]['timestamp'])
            del self.cache[oldest_key]
        
        self.cache[cache_key] = {
            'data': data,
            'timestamp': time.time()
        }
    
    async def optimized_get_user_data(self, user_id: str) -> QueryResult:
        """
        الحصول على بيانات المستخدم بشكل محسن
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            QueryResult: نتيجة الاستعلام مع معلومات الأداء
        """
        start_time = time.time()
        cache_key = f"user_data_{user_id}"
        
        # التحقق من التخزين المؤقت
        if self._is_cache_valid(cache_key):
            execution_time = time.time() - start_time
            self.query_stats['cache_hits'] += 1
            return QueryResult(
                data=self.cache[cache_key]['data'],
                execution_time=execution_time,
                cache_hit=True,
                query_complexity='simple'
            )
        
        try:
            # استعلام مجمع للحصول على جميع بيانات المستخدم
            batch_results = await self._batch_user_query(user_id)
            
            execution_time = time.time() - start_time
            self._update_cache(cache_key, batch_results)
            self._update_query_stats(execution_time)
            
            return QueryResult(
                data=batch_results,
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='complex'
            )
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على بيانات المستخدم {user_id}: {str(e)}")
            execution_time = time.time() - start_time
            return QueryResult(
                data=None,
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='error'
            )
    
    async def _batch_user_query(self, user_id: str) -> Dict[str, Any]:
        """استعلام مجمع لجميع بيانات المستخدم"""
        loop = asyncio.get_event_loop()
        
        def execute_batch():
            # إنشاء batch للاستعلامات المتعددة
            batch_results = {}
            
            # الحصول على بيانات المستخدم الأساسية
            user_ref = self.db.collection('users').document(user_id)
            user_doc = user_ref.get()
            batch_results['user'] = user_doc.to_dict() if user_doc.exists else None
            
            # الحصول على إعدادات المستخدم
            settings_ref = self.db.collection('user_settings').document(user_id)
            settings_doc = settings_ref.get()
            batch_results['settings'] = settings_doc.to_dict() if settings_doc.exists else None
            
            # الحصول على بيانات الاشتراك
            subscription_ref = self.db.collection('subscriptions').document(user_id)
            subscription_doc = subscription_ref.get()
            batch_results['subscription'] = subscription_doc.to_dict() if subscription_doc.exists else None
            
            # الحصول على مفاتيح API
            api_keys_ref = self.db.collection('user_api_keys').document(user_id)
            api_keys_doc = api_keys_ref.get()
            batch_results['api_keys'] = api_keys_doc.to_dict() if api_keys_doc.exists else None
            
            return batch_results
        
        return await loop.run_in_executor(self.executor, execute_batch)
    
    async def optimized_get_active_subscriptions(self, limit: int = 100) -> QueryResult:
        """
        الحصول على الاشتراكات النشطة بشكل محسن
        
        Args:
            limit: عدد النتائج المطلوبة
            
        Returns:
            QueryResult: نتيجة الاستعلام
        """
        start_time = time.time()
        cache_key = f"active_subscriptions_{limit}"
        
        # التحقق من التخزين المؤقت
        if self._is_cache_valid(cache_key):
            execution_time = time.time() - start_time
            self.query_stats['cache_hits'] += 1
            return QueryResult(
                data=self.cache[cache_key]['data'],
                execution_time=execution_time,
                cache_hit=True,
                query_complexity='simple'
            )
        
        try:
            loop = asyncio.get_event_loop()
            
            def execute_query():
                # استعلام محسن مع فهرسة
                subscriptions_ref = self.db.collection('subscriptions')
                query = subscriptions_ref.where(
                    filter=FieldFilter('is_active', '==', True)
                ).order_by('expires_at').limit(limit)
                
                results = []
                for doc in query.stream():
                    data = doc.to_dict()
                    data['id'] = doc.id
                    results.append(data)
                
                return results
            
            results = await loop.run_in_executor(self.executor, execute_query)
            
            execution_time = time.time() - start_time
            self._update_cache(cache_key, results)
            self._update_query_stats(execution_time)
            
            return QueryResult(
                data=results,
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='medium'
            )
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الاشتراكات النشطة: {str(e)}")
            execution_time = time.time() - start_time
            return QueryResult(
                data=[],
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='error'
            )
    
    async def optimized_get_user_transactions(self, user_id: str, status: str = None, limit: int = 50) -> QueryResult:
        """
        الحصول على معاملات المست��دم بشكل محسن
        
        Args:
            user_id: معرف المستخدم
            status: حالة المعاملة (اختياري)
            limit: عدد النتائج
            
        Returns:
            QueryResult: نتيجة الاستعلام
        """
        start_time = time.time()
        cache_key = f"user_transactions_{user_id}_{status}_{limit}"
        
        # التحقق من التخزين المؤقت
        if self._is_cache_valid(cache_key):
            execution_time = time.time() - start_time
            self.query_stats['cache_hits'] += 1
            return QueryResult(
                data=self.cache[cache_key]['data'],
                execution_time=execution_time,
                cache_hit=True,
                query_complexity='simple'
            )
        
        try:
            loop = asyncio.get_event_loop()
            
            def execute_query():
                transactions_ref = self.db.collection('transactions')
                
                # بناء الاستعلام بشكل محسن
                query = transactions_ref.where(filter=FieldFilter('user_id', '==', user_id))
                
                if status:
                    query = query.where(filter=FieldFilter('status', '==', status))
                
                query = query.order_by('created_at', direction=Query.DESCENDING).limit(limit)
                
                results = []
                for doc in query.stream():
                    data = doc.to_dict()
                    data['id'] = doc.id
                    results.append(data)
                
                return results
            
            results = await loop.run_in_executor(self.executor, execute_query)
            
            execution_time = time.time() - start_time
            self._update_cache(cache_key, results)
            self._update_query_stats(execution_time)
            
            return QueryResult(
                data=results,
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='medium'
            )
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معاملات المستخدم {user_id}: {str(e)}")
            execution_time = time.time() - start_time
            return QueryResult(
                data=[],
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='error'
            )
    
    async def optimized_batch_update(self, updates: List[Dict[str, Any]]) -> QueryResult:
        """
        تحديث مجمع محس�� للأداء
        
        Args:
            updates: قائمة التحديثات المطلوبة
            
        Returns:
            QueryResult: نتيجة العملية
        """
        start_time = time.time()
        
        try:
            loop = asyncio.get_event_loop()
            
            def execute_batch_update():
                batch = self.db.batch()
                
                for update in updates:
                    collection = update.get('collection')
                    document_id = update.get('document_id')
                    data = update.get('data')
                    operation = update.get('operation', 'update')  # update, set, delete
                    
                    doc_ref = self.db.collection(collection).document(document_id)
                    
                    if operation == 'update':
                        batch.update(doc_ref, data)
                    elif operation == 'set':
                        batch.set(doc_ref, data)
                    elif operation == 'delete':
                        batch.delete(doc_ref)
                
                # تنفيذ جميع العمليات في batch واحد
                batch.commit()
                return len(updates)
            
            result = await loop.run_in_executor(self.executor, execute_batch_update)
            
            execution_time = time.time() - start_time
            self._update_query_stats(execution_time)
            
            # إزالة البيانات المتأثرة من التخزين المؤقت
            self._invalidate_related_cache(updates)
            
            return QueryResult(
                data={'updated_count': result},
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='complex'
            )
            
        except Exception as e:
            logger.error(f"خطأ في التحديث المجمع: {str(e)}")
            execution_time = time.time() - start_time
            return QueryResult(
                data={'updated_count': 0, 'error': str(e)},
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='error'
            )
    
    async def optimized_aggregate_query(self, collection: str, aggregations: List[Dict]) -> QueryResult:
        """
        استعلام تجميعي محسن
        
        Args:
            collection: اسم المجموعة
            aggregations: قائمة عمليات التجميع
            
        Returns:
            QueryResult: نتيجة التجميع
        """
        start_time = time.time()
        cache_key = f"aggregate_{collection}_{hash(str(aggregations))}"
        
        # التحقق من التخزين المؤقت
        if self._is_cache_valid(cache_key):
            execution_time = time.time() - start_time
            self.query_stats['cache_hits'] += 1
            return QueryResult(
                data=self.cache[cache_key]['data'],
                execution_time=execution_time,
                cache_hit=True,
                query_complexity='simple'
            )
        
        try:
            loop = asyncio.get_event_loop()
            
            def execute_aggregation():
                results = {}
                
                for agg in aggregations:
                    agg_type = agg.get('type')  # count, sum, avg, max, min
                    field = agg.get('field')
                    filters = agg.get('filters', {})
                    
                    query = self.db.collection(collection)
                    
                    # تطبيق الفلاتر
                    for filter_field, filter_value in filters.items():
                        query = query.where(filter=FieldFilter(filter_field, '==', filter_value))
                    
                    docs = list(query.stream())
                    
                    if agg_type == 'count':
                        results[f'count_{field or "all"}'] = len(docs)
                    elif agg_type in ['sum', 'avg', 'max', 'min'] and field:
                        values = [doc.to_dict().get(field, 0) for doc in docs if doc.to_dict().get(field) is not None]
                        
                        if values:
                            if agg_type == 'sum':
                                results[f'sum_{field}'] = sum(values)
                            elif agg_type == 'avg':
                                results[f'avg_{field}'] = sum(values) / len(values)
                            elif agg_type == 'max':
                                results[f'max_{field}'] = max(values)
                            elif agg_type == 'min':
                                results[f'min_{field}'] = min(values)
                        else:
                            results[f'{agg_type}_{field}'] = 0
                
                return results
            
            results = await loop.run_in_executor(self.executor, execute_aggregation)
            
            execution_time = time.time() - start_time
            self._update_cache(cache_key, results)
            self._update_query_stats(execution_time)
            
            return QueryResult(
                data=results,
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='complex'
            )
            
        except Exception as e:
            logger.error(f"خطأ في الاستعلام التجميعي: {str(e)}")
            execution_time = time.time() - start_time
            return QueryResult(
                data={},
                execution_time=execution_time,
                cache_hit=False,
                query_complexity='error'
            )
    
    def _invalidate_related_cache(self, updates: List[Dict[str, Any]]):
        """إزالة البيانات المتأثرة من التخزين المؤقت"""
        affected_collections = set()
        affected_users = set()
        
        for update in updates:
            collection = update.get('collection')
            data = update.get('data', {})
            
            affected_collections.add(collection)
            
            if 'user_id' in data:
                affected_users.add(data['user_id'])
        
        # إزالة البيانات المتأثرة من التخزين المؤقت
        keys_to_remove = []
        for cache_key in self.cache.keys():
            for collection in affected_collections:
                if collection in cache_key:
                    keys_to_remove.append(cache_key)
                    break
            
            for user_id in affected_users:
                if f"user_data_{user_id}" in cache_key:
                    keys_to_remove.append(cache_key)
                    break
        
        for key in keys_to_remove:
            if key in self.cache:
                del self.cache[key]
        
        logger.info(f"تم إزالة {len(keys_to_remove)} عنصر من التخزين المؤقت")
    
    def _update_query_stats(self, execution_time: float):
        """تحديث إحصائيات الاستعلامات"""
        self.query_stats['total_queries'] += 1
        
        # تحديث متوسط وقت التنفيذ
        total_time = self.query_stats['avg_execution_time'] * (self.query_stats['total_queries'] - 1)
        self.query_stats['avg_execution_time'] = (total_time + execution_time) / self.query_stats['total_queries']
        
        # تتبع الاستعلامات البطيئة (أكثر من ثانية واحدة)
        if execution_time > 1.0:
            self.query_stats['slow_queries'].append({
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat()
            })
            
            # الاحتفاظ بآخر 100 استعلام بطيء فقط
            if len(self.query_stats['slow_queries']) > 100:
                self.query_stats['slow_queries'] = self.query_stats['slow_queries'][-100:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        cache_hit_rate = (self.query_stats['cache_hits'] / max(self.query_stats['total_queries'], 1)) * 100
        
        return {
            'total_queries': self.query_stats['total_queries'],
            'cache_hits': self.query_stats['cache_hits'],
            'cache_hit_rate': f"{cache_hit_rate:.2f}%",
            'avg_execution_time': f"{self.query_stats['avg_execution_time']:.3f}s",
            'slow_queries_count': len(self.query_stats['slow_queries']),
            'cache_size': len(self.cache),
            'recommended_indexes': self.recommended_indexes
        }
    
    def cleanup_cache(self):
        """تنظيف التخزين المؤقت المنتهي الصلاحية"""
        current_time = time.time()
        expired_keys = [
            key for key, value in self.cache.items()
            if (current_time - value['timestamp']) > self.cache_timeout
        ]
        
        for key in expired_keys:
            del self.cache[key]
        
        logger.info(f"تم حذف {len(expired_keys)} عنصر منتهي الصلاحية من التخزين الم��قت")
    
    def __del__(self):
        """تنظيف الموارد"""
        try:
            self.executor.shutdown(wait=False)
        except:
            pass


# دالة مساعدة لإنشاء مثيل محسن
def create_optimized_db_manager(db: firestore.Client) -> OptimizedFirestoreManager:
    """إنشاء مدير قاعدة بيانات محسن"""
    return OptimizedFirestoreManager(db)
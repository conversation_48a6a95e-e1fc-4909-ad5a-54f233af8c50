#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة التواريخ في نظام منع التكرار
"""

import sys
import os
from datetime import datetime, timezone, timedelta

def normalize_datetime(dt: datetime) -> datetime:
    """توحيد التعامل مع التواريخ - إزالة معلومات المنطقة الزمنية للمقارنة"""
    if dt is None:
        return datetime.now()

    # إذا كان التاريخ يحتوي على معلومات المنطقة الزمنية، قم بتحويله إلى UTC ثم إزالة المنطقة الزمنية
    if dt.tzinfo is not None:
        # تحويل إلى UTC أولاً
        dt_utc = dt.astimezone(timezone.utc)
        # إزالة معلومات المنطقة الزمنية
        return dt_utc.replace(tzinfo=None)

    # إذا لم يكن يحتوي على معلومات المنطقة الزمنية، إرجاعه كما هو
    return dt

def test_datetime_normalization():
    """اختبار توحيد التواريخ"""
    print("🧪 اختبار توحيد التواريخ...")

    # تاريخ بدون معلومات المنطقة الزمنية
    naive_dt = datetime.now()
    print(f"تاريخ بدون منطقة زمنية: {naive_dt}")

    # تاريخ مع معلومات المنطقة الزمنية
    aware_dt = datetime.now(timezone.utc)
    print(f"تاريخ مع منطقة زمنية: {aware_dt}")

    # توحيد التواريخ
    normalized_naive = normalize_datetime(naive_dt)
    normalized_aware = normalize_datetime(aware_dt)

    print(f"تاريخ موحد (naive): {normalized_naive}")
    print(f"تاريخ موحد (aware): {normalized_aware}")

    # التحقق من أن كلا التاريخين لا يحتويان على معلومات المنطقة الزمنية
    assert normalized_naive.tzinfo is None, "التاريخ الموحد يجب ألا يحتوي على معلومات المنطقة الزمنية"
    assert normalized_aware.tzinfo is None, "التاريخ الموحد يجب ألا يحتوي على معلومات المنطقة الزمنية"

    print("✅ اختبار توحيد التواريخ نجح!")

def test_datetime_comparison():
    """اختبار مقارنة التواريخ"""
    print("\n🧪 اختبار مقارنة التواريخ...")

    # إنشاء تواريخ مختلفة
    naive_dt = datetime.now()
    aware_dt = datetime.now(timezone.utc)

    # توحيد التواريخ
    normalized_naive = normalize_datetime(naive_dt)
    normalized_aware = normalize_datetime(aware_dt)

    # محاولة مقارنة التواريخ (هذا كان يسبب الخطأ سابقاً)
    try:
        # هذا كان يسبب خطأ: can't subtract offset-naive and offset-aware datetimes
        current_time = datetime.now()

        # الطريقة الخاطئة (تسبب خطأ):
        # time_diff = current_time - aware_dt  # خطأ!

        # الطريقة الصحيحة:
        time_diff_naive = current_time - normalized_naive
        time_diff_aware = current_time - normalized_aware

        print(f"فرق الوقت (naive): {time_diff_naive}")
        print(f"فرق الوقت (aware): {time_diff_aware}")

        print("✅ اختبار مقارنة التواريخ نجح!")

    except Exception as e:
        print(f"❌ خطأ في اختبار مقارنة التواريخ: {e}")
        raise

def test_age_calculation():
    """اختبار حساب عمر الأخبار"""
    print("\n🧪 اختبار حساب عمر الأخبار...")

    # إنشاء تواريخ مختلفة
    current_time = datetime.now()

    # خبر قديم بدون معلومات المنطقة الزمنية
    old_naive_dt = current_time - timedelta(hours=5)

    # خبر قديم مع معلومات المنطقة الزمنية
    old_aware_dt = (current_time - timedelta(hours=5)).replace(tzinfo=timezone.utc)

    try:
        # حساب العمر بالطريقة الصحيحة
        normalized_old_naive = normalize_datetime(old_naive_dt)
        normalized_old_aware = normalize_datetime(old_aware_dt)

        age_naive = current_time - normalized_old_naive
        age_aware = current_time - normalized_old_aware

        print(f"عمر الخبر (naive): {age_naive}")
        print(f"عمر الخبر (aware): {age_aware}")

        # التحقق من أن العمر منطقي
        assert age_naive.total_seconds() > 0, "عمر الخبر يجب أن يكون موجباً"
        assert age_aware.total_seconds() > 0, "عمر الخبر يجب أن يكون موجباً"

        print("✅ اختبار حساب عمر الأخبار نجح!")

    except Exception as e:
        print(f"❌ خطأ في اختبار حساب عمر الأخبار: {e}")
        raise

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاح مشكلة التواريخ...")
    
    try:
        test_datetime_normalization()
        test_datetime_comparison()
        test_age_calculation()
        
        print("\n🎉 جميع الاختبارات نجحت! تم إصلاح مشكلة التواريخ بنجاح.")
        
    except Exception as e:
        print(f"\n❌ فشل في الاختبار: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

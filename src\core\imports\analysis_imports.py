"""
📊 استيرادات وحدات التحليل
==========================

جميع وحدات التحليل والذكاء الاصطناعي في النظام.
هذه الوحدات تدير التحليل الفني والأساسي للعملات الرقمية.

الفئات:
- التحليل الأساسي
- التحليل المحسن
- التحليل بالذكاء الاصطناعي
- المؤشرات المحسنة
- أدوات التحليل المساعدة
- تكامل البيانات
"""

# ===== التحليل الأساسي =====
try:
    from analysis.basic_analysis import (
        CryptoAnalysis,
        analyze_symbol,
        analyze_command,
        add_indicator,
        remove_indicator,
        add_custom_currency,
        customize_indicators,
        initialize_basic_analysis
    )
    BASIC_ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import basic analysis: {e}")
    BASIC_ANALYSIS_AVAILABLE = False

# ===== التحليل المحسن =====
try:
    from analysis.enhanced_analysis import (
        initialize_enhanced_analysis,
        show_enhanced_stats,
        show_enhanced_analysis_menu,
        analyze_symbol_enhanced,
        compare_trading_styles,
        refresh_analysis,
        show_analysis_type_settings,
        get_comprehensive_analysis
    )
    ENHANCED_ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import enhanced analysis: {e}")
    ENHANCED_ANALYSIS_AVAILABLE = False

# ===== التحليل بالذكاء الاصطناعي =====
try:
    from analysis.gemini_analysis import (
        analyze_with_gemini, 
        generate_smart_alerts, 
        get_user_api_client, 
        verify_gemini_api
    )
    from analysis.enhanced_ai_analysis import (
        EnhancedAIAnalyzer, 
        initialize_enhanced_ai_analyzer
    )
    AI_ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import AI analysis: {e}")
    AI_ANALYSIS_AVAILABLE = False

# ===== المؤشرات المحسنة =====
try:
    from analysis.optimized_indicators import OptimizedIndicators
    OPTIMIZED_INDICATORS_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import optimized indicators: {e}")
    OPTIMIZED_INDICATORS_AVAILABLE = False

# ===== أدوات التحليل المساعدة =====
try:
    from analysis.analysis_helpers import (
        initialize_analysis_helpers,
        create_analysis_text,
        generate_stats_report,
        get_analysis_type_name
    )
    ANALYSIS_HELPERS_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import analysis helpers: {e}")
    ANALYSIS_HELPERS_AVAILABLE = False

# ===== تكامل البيانات =====
try:
    from analysis.user_market_data import get_market_data_with_user_api
    from analysis.integration_wrapper import EnhancedAnalysisWrapper
    DATA_INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"WARNING: Failed to import data integration: {e}")
    DATA_INTEGRATION_AVAILABLE = False

# تجميع جميع وحدات التحليل
all_analysis_functions = {
    'basic_analysis': {
        'available': BASIC_ANALYSIS_AVAILABLE,
        'functions': [
            'CryptoAnalysis', 'analyze_symbol', 'analyze_command',
            'add_indicator', 'remove_indicator', 'add_custom_currency',
            'customize_indicators', 'initialize_basic_analysis'
        ],
        'critical': True
    },
    'enhanced_analysis': {
        'available': ENHANCED_ANALYSIS_AVAILABLE,
        'functions': [
            'initialize_enhanced_analysis', 'show_enhanced_stats',
            'show_enhanced_analysis_menu', 'analyze_symbol_enhanced',
            'compare_trading_styles', 'refresh_analysis',
            'show_analysis_type_settings', 'get_comprehensive_analysis'
        ],
        'critical': True
    },
    'ai_analysis': {
        'available': AI_ANALYSIS_AVAILABLE,
        'functions': [
            'analyze_with_gemini', 'generate_smart_alerts',
            'get_user_api_client', 'verify_gemini_api',
            'EnhancedAIAnalyzer', 'initialize_enhanced_ai_analyzer'
        ],
        'critical': True
    },
    'optimized_indicators': {
        'available': OPTIMIZED_INDICATORS_AVAILABLE,
        'functions': ['OptimizedIndicators'],
        'critical': False
    },
    'analysis_helpers': {
        'available': ANALYSIS_HELPERS_AVAILABLE,
        'functions': [
            'initialize_analysis_helpers', 'create_analysis_text',
            'generate_stats_report', 'get_analysis_type_name'
        ],
        'critical': True
    },
    'data_integration': {
        'available': DATA_INTEGRATION_AVAILABLE,
        'functions': [
            'get_market_data_with_user_api', 'EnhancedAnalysisWrapper'
        ],
        'critical': True
    }
}

__all__ = ['all_analysis_functions'] + [
    f"{category.upper()}_AVAILABLE" for category in [
        'basic_analysis', 'enhanced_analysis', 'ai_analysis',
        'optimized_indicators', 'analysis_helpers', 'data_integration'
    ]
]

def get_analysis_status():
    """
    حالة توفر جميع وحدات التحليل
    
    Returns:
        dict: حالة كل وحدة تحليل
    """
    return {
        category: {
            'available': info['available'],
            'critical': info['critical'],
            'functions_count': len(info['functions'])
        }
        for category, info in all_analysis_functions.items()
    }

def validate_critical_analysis():
    """
    التحقق من توفر وحدات التحليل الحرجة
    
    Returns:
        tuple: (success: bool, missing_critical: list)
    """
    missing_critical = []
    
    for category, info in all_analysis_functions.items():
        if info['critical'] and not info['available']:
            missing_critical.append(category)
    
    return len(missing_critical) == 0, missing_critical

def get_available_analysis_functions():
    """
    الحصول على قائمة بجميع دوال التحليل المتاحة
    
    Returns:
        dict: دوال التحليل المتاحة مجمعة حسب الفئة
    """
    available_functions = {}
    
    for category, info in all_analysis_functions.items():
        if info['available']:
            available_functions[category] = info['functions']
    
    return available_functions

def get_analysis_capabilities():
    """
    الحصول على قدرات التحليل المتاحة
    
    Returns:
        dict: قدرات النظام التحليلية
    """
    capabilities = {
        'basic_technical_analysis': BASIC_ANALYSIS_AVAILABLE,
        'enhanced_multi_timeframe': ENHANCED_ANALYSIS_AVAILABLE,
        'ai_powered_analysis': AI_ANALYSIS_AVAILABLE,
        'optimized_indicators': OPTIMIZED_INDICATORS_AVAILABLE,
        'comprehensive_reporting': ANALYSIS_HELPERS_AVAILABLE,
        'real_time_data': DATA_INTEGRATION_AVAILABLE
    }
    
    # حساب النسبة المئوية للقدرات المتاحة
    available_count = sum(1 for available in capabilities.values() if available)
    total_count = len(capabilities)
    availability_percentage = (available_count / total_count) * 100
    
    return {
        'capabilities': capabilities,
        'availability_percentage': availability_percentage,
        'available_count': available_count,
        'total_count': total_count
    }

# اختبار فوري لوحدات التحليل
if __name__ == "__main__":
    success, missing = validate_critical_analysis()
    if success:
        print("✅ جميع وحدات التحليل الحرجة متوفرة")
    else:
        print(f"❌ وحدات تحليل حرجة مفقودة: {missing}")
        
    # عرض قدرات التحليل
    capabilities = get_analysis_capabilities()
    print(f"\n📊 قدرات التحليل المتاحة: {capabilities['availability_percentage']:.1f}%")
    print(f"({capabilities['available_count']}/{capabilities['total_count']} قدرات متاحة)")
    
    # عرض حالة كل وحدة
    status = get_analysis_status()
    print("\n📈 حالة وحدات التحليل:")
    for category, info in status.items():
        status_icon = "✅" if info['available'] else "❌"
        critical_icon = "🔴" if info['critical'] else "🟡"
        print(f"{status_icon} {critical_icon} {category}: {info['functions_count']} دالة")

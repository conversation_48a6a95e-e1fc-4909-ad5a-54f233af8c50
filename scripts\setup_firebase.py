#!/usr/bin/env python3
"""
Firebase Setup Script
=====================

Create required Firestore indexes for trading system

Author: Augment Agent
"""

import os
import sys
import json
from datetime import datetime

def create_firestore_indexes():
    """Create Firestore indexes configuration file"""
    
    indexes = {
        "indexes": [
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {
                        "fieldPath": "status",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "user_id", 
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at",
                        "order": "DESCENDING"
                    }
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION", 
                "fields": [
                    {
                        "fieldPath": "user_id",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at",
                        "order": "DESCENDING"
                    }
                ]
            },
            {
                "collectionGroup": "trading_recommendations",
                "queryScope": "COLLECTION",
                "fields": [
                    {
                        "fieldPath": "symbol",
                        "order": "ASCENDING"
                    },
                    {
                        "fieldPath": "created_at", 
                        "order": "DESCENDING"
                    }
                ]
            }
        ],
        "fieldOverrides": []
    }
    
    # Create src/config directory if it doesn't exist
    config_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src', 'config')
    os.makedirs(config_dir, exist_ok=True)
    
    # Save indexes file
    indexes_file = os.path.join(config_dir, 'firestore.indexes.json')
    
    with open(indexes_file, 'w', encoding='utf-8') as f:
        json.dump(indexes, f, indent=2, ensure_ascii=False)
    
    print(f"Created indexes file: {indexes_file}")
    return indexes_file

def print_manual_instructions():
    """Print manual setup instructions"""
    
    print("\n" + "="*60)
    print("MANUAL FIREBASE INDEXES SETUP INSTRUCTIONS")
    print("="*60)
    
    print("\n1. Go to Firebase Console:")
    print("   https://console.firebase.google.com/")
    
    print("\n2. Select your project")
    
    print("\n3. Navigate to Firestore Database > Indexes")
    
    print("\n4. Click 'Create Index' and add the following indexes:")
    
    indexes_info = [
        {
            "name": "Main Trading Recommendations Index",
            "collection": "trading_recommendations",
            "fields": [
                "status (Ascending)",
                "user_id (Ascending)", 
                "created_at (Descending)"
            ]
        },
        {
            "name": "User Recommendations Index",
            "collection": "trading_recommendations",
            "fields": [
                "user_id (Ascending)",
                "created_at (Descending)"
            ]
        },
        {
            "name": "Symbol Recommendations Index",
            "collection": "trading_recommendations", 
            "fields": [
                "symbol (Ascending)",
                "created_at (Descending)"
            ]
        }
    ]
    
    for i, index in enumerate(indexes_info, 1):
        print(f"\n   Index {i}: {index['name']}")
        print(f"   Collection: {index['collection']}")
        print("   Fields:")
        for field in index['fields']:
            print(f"     - {field}")
    
    print("\n5. Wait for all indexes to be created")
    print("\n6. Check index status in the 'Indexes' section")

def main():
    """Main function"""
    
    print("Starting Firebase setup...")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Create indexes file
        indexes_file = create_firestore_indexes()
        print(f"Successfully created indexes file: {indexes_file}")
        
        # Print manual instructions
        print_manual_instructions()
        
        print("\n" + "="*60)
        print("FIREBASE SETUP COMPLETED!")
        print("="*60)
        print("\nNext steps:")
        print("1. Follow the instructions above to create indexes manually")
        print("2. Run tests to verify system functionality")
        print("3. Monitor performance in Firebase Console")
        
        return True
        
    except Exception as e:
        print(f"Error setting up Firebase: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

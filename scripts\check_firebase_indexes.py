#!/usr/bin/env python3
"""
Check Firebase Indexes Status
=============================

Check if Firebase indexes are created and working

Author: Augment Agent
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FirebaseIndexChecker:
    """Firebase index checking class"""
    
    def __init__(self):
        self.db = None
        
    async def initialize_firebase(self):
        """Initialize Firebase connection"""
        try:
            import firebase_admin
            from firebase_admin import credentials, firestore
            
            # Check if Firebase is already initialized
            try:
                app = firebase_admin.get_app()
                logger.info("Firebase already initialized")
            except ValueError:
                # Try to initialize Firebase
                try:
                    # Try default credentials first
                    cred = credentials.ApplicationDefault()
                    firebase_admin.initialize_app(cred)
                    logger.info("Firebase initialized with default credentials")
                except Exception:
                    logger.warning("Could not initialize Firebase Admin SDK")
                    return False
            
            self.db = firestore.client()
            return True
            
        except ImportError:
            logger.error("Firebase Admin SDK not installed")
            logger.info("Install with: pip install firebase-admin")
            return False
        except Exception as e:
            logger.error(f"Error initializing Firebase: {str(e)}")
            return False
    
    async def test_basic_queries(self):
        """Test basic Firestore queries"""
        logger.info("Testing basic Firestore queries...")
        
        try:
            # Test simple collection access
            collections = list(self.db.collections())
            collection_names = [col.id for col in collections]
            
            logger.info(f"Available collections: {collection_names}")
            
            # Test trading_recommendations collection
            if 'trading_recommendations' in collection_names:
                logger.info("✅ trading_recommendations collection exists")
                return True
            else:
                logger.warning("⚠️ trading_recommendations collection not found")
                return False
                
        except Exception as e:
            logger.error(f"Error testing basic queries: {str(e)}")
            return False
    
    async def test_index_queries(self):
        """Test queries that require indexes"""
        logger.info("Testing index-dependent queries...")
        
        try:
            collection_ref = self.db.collection('trading_recommendations')
            
            # Test queries that require composite indexes
            test_queries = [
                {
                    'name': 'User + Status + Created',
                    'query': collection_ref.where('user_id', '==', 'test_user')
                                          .where('status', '==', 'active')
                                          .order_by('created_at', direction='DESCENDING')
                                          .limit(1)
                },
                {
                    'name': 'User + Created',
                    'query': collection_ref.where('user_id', '==', 'test_user')
                                          .order_by('created_at', direction='DESCENDING')
                                          .limit(1)
                },
                {
                    'name': 'Symbol + Created',
                    'query': collection_ref.where('symbol', '==', 'BTC')
                                          .order_by('created_at', direction='DESCENDING')
                                          .limit(1)
                }
            ]
            
            results = {}
            
            for test in test_queries:
                try:
                    # Try to execute the query
                    docs = list(test['query'].stream())
                    results[test['name']] = {
                        'status': 'SUCCESS',
                        'doc_count': len(docs)
                    }
                    logger.info(f"✅ {test['name']}: Query executed successfully")
                    
                except Exception as e:
                    error_msg = str(e)
                    if 'index' in error_msg.lower():
                        results[test['name']] = {
                            'status': 'INDEX_MISSING',
                            'error': 'Composite index required'
                        }
                        logger.error(f"❌ {test['name']}: Index missing")
                    else:
                        results[test['name']] = {
                            'status': 'ERROR',
                            'error': error_msg
                        }
                        logger.error(f"❌ {test['name']}: {error_msg}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error testing index queries: {str(e)}")
            return {'error': str(e)}
    
    async def create_test_documents(self):
        """Create test documents for index testing"""
        logger.info("Creating test documents...")
        
        try:
            collection_ref = self.db.collection('trading_recommendations')
            
            test_docs = [
                {
                    'user_id': 'test_user_1',
                    'symbol': 'BTC',
                    'status': 'active',
                    'created_at': datetime.now(),
                    'test_document': True
                },
                {
                    'user_id': 'test_user_2', 
                    'symbol': 'ETH',
                    'status': 'completed',
                    'created_at': datetime.now(),
                    'test_document': True
                }
            ]
            
            doc_ids = []
            for doc_data in test_docs:
                doc_ref = collection_ref.add(doc_data)
                doc_ids.append(doc_ref[1].id)
                logger.info(f"Created test document: {doc_ref[1].id}")
            
            return doc_ids
            
        except Exception as e:
            logger.error(f"Error creating test documents: {str(e)}")
            return []
    
    async def cleanup_test_documents(self, doc_ids):
        """Clean up test documents"""
        logger.info("Cleaning up test documents...")
        
        try:
            collection_ref = self.db.collection('trading_recommendations')
            
            for doc_id in doc_ids:
                collection_ref.document(doc_id).delete()
                logger.info(f"Deleted test document: {doc_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error cleaning up test documents: {str(e)}")
            return False
    
    def print_index_status(self, query_results):
        """Print index status summary"""
        logger.info("\n" + "="*60)
        logger.info("FIREBASE INDEXES STATUS")
        logger.info("="*60)
        
        if isinstance(query_results, dict) and 'error' not in query_results:
            total_queries = len(query_results)
            successful_queries = sum(1 for result in query_results.values() 
                                   if result.get('status') == 'SUCCESS')
            missing_indexes = sum(1 for result in query_results.values() 
                                if result.get('status') == 'INDEX_MISSING')
            
            logger.info(f"Total queries tested: {total_queries}")
            logger.info(f"Successful queries: {successful_queries}")
            logger.info(f"Missing indexes: {missing_indexes}")
            
            if missing_indexes == 0:
                logger.info("🎉 All indexes are working correctly!")
                return True
            else:
                logger.warning("⚠️ Some indexes are missing. Run:")
                logger.warning("firebase deploy --only firestore:indexes")
                return False
        else:
            logger.error("❌ Could not test indexes properly")
            return False

async def main():
    """Main function"""
    logger.info("Starting Firebase indexes check...")
    
    checker = FirebaseIndexChecker()
    
    # Initialize Firebase
    firebase_ready = await checker.initialize_firebase()
    
    if not firebase_ready:
        logger.error("Cannot proceed without Firebase connection")
        logger.info("\nTroubleshooting:")
        logger.info("1. Make sure you have Firebase Admin SDK: pip install firebase-admin")
        logger.info("2. Set up authentication (service account or default credentials)")
        logger.info("3. Check your Firebase project configuration")
        return False
    
    # Test basic queries
    basic_ok = await checker.test_basic_queries()
    
    if not basic_ok:
        logger.warning("Basic queries failed - creating test collection")
        # Create test documents
        doc_ids = await checker.create_test_documents()
    else:
        doc_ids = []
    
    # Test index queries
    query_results = await checker.test_index_queries()
    
    # Print status
    indexes_ok = checker.print_index_status(query_results)
    
    # Cleanup
    if doc_ids:
        await checker.cleanup_test_documents(doc_ids)
    
    logger.info("\n" + "="*60)
    logger.info("NEXT STEPS")
    logger.info("="*60)
    
    if indexes_ok:
        logger.info("✅ All Firebase indexes are working!")
        logger.info("Your trading system is ready to use.")
    else:
        logger.info("🔧 To create missing indexes:")
        logger.info("1. Run: firebase deploy --only firestore:indexes")
        logger.info("2. Wait for indexes to build (can take a few minutes)")
        logger.info("3. Run this script again to verify")
    
    return indexes_ok

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

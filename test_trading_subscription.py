#!/usr/bin/env python3
"""
اختبار نظام التداول مع الاشتراكات واليوم المجاني
"""

import asyncio
import sys
import os

# إضافة مسار src للاستيراد
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_trading_subscription():
    """اختبار نظام التداول مع الاشتراكات"""
    
    print("Testing trading system with subscriptions...")
    print("=" * 60)
    
    try:
        # تهيئة Firebase
        from integrations.firebase_init import initialize_firebase
        db = initialize_firebase()
        print("SUCCESS: Firebase initialized")

        # تهيئة free_day_system
        from services.free_day_system import FreeDaySystem
        free_day_system = FreeDaySystem(db)
        print("SUCCESS: free_day_system initialized")

        # تهيئة subscription_system
        from services.subscription_system import SubscriptionSystem
        subscription_system = SubscriptionSystem(db, free_day_system, None)
        print("SUCCESS: subscription_system initialized")
        
        # تهيئة TradingHandlers
        from trading.trading_db import TradingDatabaseManager
        from trading.auto_trading_system import AutoTradingSystem
        from handlers.trading_handlers import TradingHandlers
        
        # إنشاء مكونات وهمية للاختبار
        trading_db_manager = TradingDatabaseManager(db)
        auto_trading_system = AutoTradingSystem(trading_db_manager, None, None, None, None)
        trading_handlers = TradingHandlers(trading_db_manager, auto_trading_system, subscription_system)
        
        print("SUCCESS: TradingHandlers initialized")

        # اختبار المستخدم
        test_user_id = "123456789"

        print(f"\nTesting subscription check for user {test_user_id}...")

        # اختبار _check_premium_subscription
        result = await trading_handlers._check_premium_subscription(test_user_id)
        print(f"Subscription check result: {result}")

        # اختبار منح يوم مجاني
        print(f"\nGranting 1 hour free trial to user {test_user_id}...")
        free_day_granted = free_day_system.grant_free_day(test_user_id, duration_hours=1)
        print(f"Free day grant result: {free_day_granted}")
        
        if free_day_granted:
            # اختبار فحص الاشتراك بعد منح اليوم المجاني
            print(f"\nTesting subscription check after granting free day...")
            result_after_grant = await trading_handlers._check_premium_subscription(test_user_id)
            print(f"Subscription check result after free day grant: {result_after_grant}")

            if result_after_grant:
                print("SUCCESS! User can access auto trading after free day grant")
            else:
                print("FAILED! User cannot access auto trading despite free day grant")

        print("\nTest completed successfully!")
        
    except Exception as e:
        print(f"ERROR in test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_trading_subscription())

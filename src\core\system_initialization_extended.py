"""
وحدة تهيئة النظام المحسنة - الإصدار الموسع
تحتوي على جميع دوال التهيئة المنقولة من main.py
"""

import logging
import os
import uuid
from datetime import datetime
from typing import Optional, Any

logger = logging.getLogger(__name__)

# علامة للتحقق من التهيئة الموسعة لتجنب التهيئة المتكررة
_extended_system_initialized = False
_system_components_cache = None

async def initialize_firestore(db):
    """تهيئة قاعدة البيانات في Firestore"""
    try:
        # التحقق من وجود المجموعات
        existing_collections = [collection.id for collection in db.collections()]

        # تكوين النظام الأساسي
        system_config = {
            '_metadata': {
                'version': '1.0',
                'last_update': datetime.now().isoformat(),
                'initialized': True
            },
            '_schema': {
                'users': {
                    'userId': 'string',
                    'username': 'string',
                    'chatId': 'number',
                    'subscriptionStatus': 'string',
                    'subscriptionExpiry': 'timestamp',
                    'language': 'string',
                    'createdAt': 'timestamp'
                },
                'transactions': {
                    'userId': 'string',
                    'amount': 'number',
                    'status': 'string',
                    'createdAt': 'timestamp'
                },
                'alerts': {
                    'userId': 'string',
                    'symbol': 'string',
                    'price': 'number',
                    'condition': 'string',
                    'createdAt': 'timestamp'
                },
                'user_settings': {
                    'userId': 'string',
                    'language': 'string'
                }
            }
        }

        # إنشاء أو تحديث تكوين النظام
        config_ref = db.collection('_system').document('config')
        if not config_ref.get().exists:
            config_ref.set(system_config)
            logger.info("تم إنشاء تكوين النظام")

        # المجموعات الأساسية التي نحتاج لإنشائها
        required_collections = [
            'users',
            'transactions',
            'subscriptions',
            'alerts',
            'user_settings'       # إعدادات المستخدم
        ]

        # إنشاء المجموعات الناقصة
        for collection_name in required_collections:
            if collection_name not in existing_collections:
                # إنشاء وثيقة تهيئة للمجموعة
                init_doc = {
                    '_metadata': {
                        'created_at': datetime.now().isoformat(),
                        'collection_type': collection_name
                    }
                }
                db.collection(collection_name).document('_init').set(init_doc)
                logger.info(f"تم إنشاء مجموعة {collection_name}")

        # تحديث حالة التهيئة
        config_ref.update({
            '_metadata.last_update': datetime.now().isoformat(),
            '_metadata.collections': required_collections
        })

        logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return False

def check_firestore_connection(db):
    """التحقق من الاتصال بـ Firestore"""
    try:
        # التحقق مباشرة من الاتصال باستخدام كائن db المهيأ مسبقًا
        if not db:
            raise Exception("كائن قاعدة بيانات Firestore غير مهيأ")

        logger.info("🔄 جاري التحقق من الاتصال بـ Firestore باستخدام كائن db المهيأ...")

        # التحقق من الاتصال
        test_ref = db.collection('test').document('connection')

        # اختبار الكتابة
        test_data = {
            'test': 'connection',
            'timestamp': datetime.now().isoformat(),
            'random_id': str(uuid.uuid4())
        }
        logger.info("📝 جاري اختبار الكتابة في Firestore...")
        test_ref.set(test_data)

        # اختبار القراءة
        logger.info("📖 جاري اختبار القراءة من Firestore...")
        read_data = test_ref.get().to_dict()
        if read_data != test_data:
            raise Exception("فشل في مطابقة البيانات المقروءة مع البيانات المكتوبة")

        # حذف بيانات الاختبار
        logger.info("🗑️ جاري تنظيف بيانات الاختبار...")
        test_ref.delete()

        logger.info("✨ تم التحقق من جميع عمليات Firestore بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في الاتصال بـ Firestore: {str(e)}")
        return False

def initialize_analysis_modules(
    subscription_system,
    api_manager,
    enhanced_analyzer,
    db,
    user_states,
    get_text,
    create_analysis_text,
    load_user_settings,
    save_user_settings,
    specialized_handlers,
    delete_message_after_delay,
    binance_manager,
    ca,
    show_main_menu,
    show_api_instructions,
    analyze_symbol
):
    """تهيئة جميع وحدات التحليل"""
    try:
        # استيراد الدوال المطلوبة
        from analysis.basic_analysis import initialize_basic_analysis
        from analysis.enhanced_analysis import initialize_enhanced_analysis

        # تهيئة وحدة التحليل الأساسي
        initialize_basic_analysis(
            subscription_system,
            api_manager,
            enhanced_analyzer,
            db,
            user_states,
            get_text,
            create_analysis_text,
            load_user_settings,
            save_user_settings,
            specialized_handlers,
            delete_message_after_delay
        )

        # تم دمج وحدة التحليل المتبقي في basic_analysis.py
        # لا حاجة لتهيئة منفصلة

        # تهيئة وحدة التحليل المحسن
        initialize_enhanced_analysis(
            subscription_system,
            enhanced_analyzer,
            api_manager,
            db,
            ca,
            user_states,
            show_main_menu,
            show_api_instructions,
            analyze_symbol
        )

        logger.info("✅ تم تهيئة جميع وحدات التحليل بنجاح")
        return True
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة وحدات التحليل: {str(e)}")
        return False

async def migrate_config_to_database(system_settings):
    """نقل الإعدادات من ملف التكوين إلى قاعدة البيانات"""
    try:
        # قائمة بالإعدادات الحساسة
        sensitive_keys = [
            "PAYPAL_CLIENT_ID",
            "PAYPAL_CLIENT_SECRET",
            "BOT_TOKEN",
            # تم تعطيل استيراد مفاتيح Binance API
            # "BINANCE_API_KEY",
            # "BINANCE_API_SECRET",
            # تم تعطيل استيراد مفتاح Gemini API
            # "GEMINI_API_KEY",
            "ENCRYPTION_KEY",
            "GITHUB_TOKEN"
        ]

        # التحقق من وجود الإعدادات في قاعدة البيانات
        settings_count = len(system_settings.get_all())
        sensitive_count = len(system_settings.get_all(sensitive=True))

        # نقوم بإعادة تعيين الإعدادات فقط إذا كانت هناك حاجة لذلك
        # إذا كان هناك إعدادات حساسة في الوثيقة العامة أو العكس، نقوم بإعادة تعيينها
        need_reset = False

        # التحقق من الإعدادات العامة
        general_settings = system_settings.get_all()
        for key, value in general_settings.items():
            # التحقق مما إذا كان الإعداد يجب أن يكون حساسًا
            if any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                logger.info(f"⚠️ تم العثور على إعداد حساس في الوثيقة العامة: {key}")
                need_reset = True
                break

        # التحقق من الإعدادات الحساسة
        sensitive_settings = system_settings.get_all(sensitive=True)
        for key, value in sensitive_settings.items():
            # التحقق مما إذا كان الإعداد يجب أن يكون عامًا
            if not any(keyword in key for keyword in ['API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH']):
                logger.info(f"⚠️ تم العثور على إعداد عام في الوثيقة الحساسة: {key}")
                need_reset = True
                break

        # إذا كانت هناك حاجة لإعادة تعيين الإعدادات أو لم تكن هناك إعدادات
        if need_reset or settings_count == 0 or sensitive_count == 0:
            # استيراد فقط متغيرات البيئة المحددة في ملف .env
            # قائمة المتغيرات المسموح بها
            allowed_env_vars = [
                # Bot Configuration
                "DEVELOPER_ID", "BOT_TOKEN",

                # Binance API - تم تعطيل استيراد مفاتيح Binance API
                # "BINANCE_API_KEY", "BINANCE_API_SECRET",

                # Gemini API - تم تعطيل استيراد مفتاح Gemini API
                # "GEMINI_API_KEY",

                # GitHub Configuration
                "GITHUB_TOKEN", "GITHUB_REPO", "GITHUB_OWNER",

                # Firebase Configuration
                "GOOGLE_APPLICATION_CREDENTIALS",

                # Default Settings
                "ANALYSES_PER_DAY", "ALERTS_PER_DAY", "DEFAULT_LANG",

                # System Settings
                "BACKUP_INTERVAL", "ENCRYPTION_KEY",

                # Health Check
                "HEALTH_CHECK_TOKEN", "HEALTH_CHECK_PORT",

                # Payment Settings
                "PAYMENT_AMOUNT",

                # Update Settings
                "AUTO_UPDATE", "UPDATE_CHANNEL",

                # Secret Key
                "SECRET_KEY",

                # PayPal Configuration
                "PAYPAL_CLIENT_ID", "PAYPAL_CLIENT_SECRET", "DEV_MODE"
            ]

            # حذف الإعدادات فقط إذا كان هناك تضارب
            if need_reset:
                system_settings.clear_all()
                system_settings.clear_all(sensitive=True)
                logger.info("✅ تم حذف الإعدادات المتضاربة وإعادة تنظيمها")
            else:
                logger.info("ℹ️ تهيئة الإعدادات للمرة الأولى")

            # استيراد المتغيرات المسموح بها فقط
            local_env_count = 0
            for key in allowed_env_vars:
                if key in os.environ:
                    value = os.environ[key]

                    # تحديد ما إذا كان الإعداد حساسًا أم لا
                    # قائمة البادئات الحساسة
                    sensitive_prefixes = [
                        'PAYPAL_', 'BOT_', 'BINANCE_', 'ENCRYPTION_', 'GITHUB_', 'API_', 'GEMINI_',
                        'SECRET_', 'TOKEN_', 'KEY_', 'PASSWORD_', 'CREDENTIAL_'
                    ]
                    # قائمة الكلمات الحساسة في أي مكان في المفتاح
                    sensitive_keywords = [
                        'API', 'KEY', 'SECRET', 'TOKEN', 'PASSWORD', 'CREDENTIAL', 'AUTH'
                    ]

                    # التحقق من البادئات
                    is_prefix_sensitive = any(key.startswith(prefix) for prefix in sensitive_prefixes)
                    # التحقق من الكلمات الحساسة في أي مكان في المفتاح
                    is_keyword_sensitive = any(keyword in key for keyword in sensitive_keywords)

                    # الإعداد حساس إذا كان يبدأ ببادئة حساسة أو يحتوي على كلمة حساسة
                    is_sensitive = is_prefix_sensitive or is_keyword_sensitive

                    # تحويل القيمة إلى النوع المناسب
                    if value.lower() == 'true':
                        value = True
                    elif value.lower() == 'false':
                        value = False
                    elif value.isdigit():
                        value = int(value)
                    elif value.replace('.', '', 1).isdigit() and value.count('.') == 1:
                        value = float(value)

                    # تعيين الإعداد
                    if system_settings.set(key, value, is_sensitive):
                        local_env_count += 1

            # تعيين علامة التهيئة
            system_settings.set("_initialized", True)

            logger.info(f"✅ تم استيراد {local_env_count} إعداد من متغيرات البيئة المحددة")
        else:
            logger.info(f"✅ تم العثور على {settings_count} إعداد عام و {sensitive_count} إعداد حساس في قاعدة البيانات")

        return True
    except Exception as e:
        logger.error(f"❌ خطأ في نقل الإعدادات من ملف التكوين إلى قاعدة البيانات: {str(e)}")
        # في حالة الخطأ، نستمر في تشغيل البوت باستخدام الإعدادات الحالية
        return False

async def initialize_system_extended(
    db,
    get_or_create_encryption_key,
    APIManager,
    binance_manager,
    BinanceTransactionVerifier,
    AutomaticTransactionVerifier,
    EnhancedAnalysisWrapper,
    initialize_subscription_system,
    free_day_system,
    initialize_analysis_helpers,
    initialize_alert_service,
    initialize_api_management_service,
    initialize_user_management,
    ca,
    user_states,
    get_text,
    show_main_menu,
    generate_stats_report,
    show_language_selection,
    show_terms_and_conditions,
    SystemConfig,
    transaction_service,
    create_analysis_text,
    load_user_settings,
    save_user_settings,
    specialized_handlers,
    delete_message_after_delay,
    show_api_instructions,
    analyze_symbol
):
    """تهيئة النظام وفحص الاتصال - النسخة الموسعة"""
    global _extended_system_initialized, _system_components_cache

    try:
        # التحقق من التهيئة لتجنب التهيئة المتكررة
        if _extended_system_initialized and _system_components_cache:
            logger.info("ℹ️ النظام الموسع مُهيأ بالفعل، إرجاع المكونات المحفوظة")
            # التحقق من صحة المكونات المحفوظة
            if all(key in _system_components_cache for key in ['encryption_key', 'api_manager', 'subscription_system']):
                logger.info("✅ المكونات المحفوظة صالحة، تخطي التهيئة")
                return _system_components_cache
            else:
                logger.warning("⚠️ المكونات المحفوظة غير مكتملة، إعادة التهيئة...")
                _extended_system_initialized = False
                _system_components_cache = None

        logger.info("🔄 جاري تهيئة النظام الموسع للمرة الأولى...")

        # التحقق من الاتصال بـ Firestore
        logger.info("🔍 جاري التحقق من الاتصال بـ Firestore...")
        if not check_firestore_connection(db):
            raise Exception("فشل في الاتصال بـ Firestore")

        # تهيئة قاعدة البيانات
        logger.info("🔧 جاري تهيئة قاعدة البيانات...")
        if not await initialize_firestore(db):
            raise Exception("فشل في تهيئة قاعدة البيانات")

        # تهيئة مفتاح التشفير
        logger.info("🔑 جاري تهيئة مفتاح التشفير...")
        encryption_key = await get_or_create_encryption_key()

        # تهيئة مدير API
        logger.info("💻 جاري تهيئة مدير API...")
        api_manager = APIManager(db, encryption_key)

        # تعيين api_manager في binance_manager
        binance_manager.set_api_manager(api_manager)

        # تهيئة binance_verifier و auto_verifier بعد تهيئة api_manager
        binance_verifier = BinanceTransactionVerifier(db, api_manager)
        auto_verifier = AutomaticTransactionVerifier(db, api_manager)

        # تهيئة النظام المحسن متعدد الإطارات الزمنية
        logger.info("🚀 جاري تهيئة النظام المحسن متعدد الإطارات الزمنية...")
        enhanced_analyzer = EnhancedAnalysisWrapper(binance_manager, api_manager)
        logger.info("✅ تم تهيئة النظام المحسن بنجاح")

        # تهيئة نظام الاشتراكات الجديد
        logger.info("💎 جاري تهيئة نظام الاشتراكات...")
        subscription_system = initialize_subscription_system(db, free_day_system, api_manager)
        logger.info("✅ تم تهيئة نظام الاشتراكات بنجاح")

        # تحديث خدمة المعاملات مع نظام الاشتراكات
        transaction_service.subscription_system = subscription_system

        # تهيئة دوال التحليل المساعدة
        logger.info("🔧 جاري تهيئة دوال التحليل المساعدة...")
        initialize_analysis_helpers(subscription_system, api_manager, enhanced_analyzer, db)
        logger.info("✅ تم تهيئة دوال التحليل المساعدة بنجاح")

        # تهيئة وحدات التحليل (basic_analysis, enhanced_analysis, etc.)
        logger.info("📊 جاري تهيئة وحدات التحليل...")
        analysis_modules_success = initialize_analysis_modules(
            subscription_system,
            api_manager,
            enhanced_analyzer,
            db,
            user_states,
            get_text,
            create_analysis_text,
            load_user_settings,
            save_user_settings,
            specialized_handlers,
            delete_message_after_delay,
            binance_manager,
            ca,
            show_main_menu,
            show_api_instructions,
            analyze_symbol
        )
        if analysis_modules_success:
            logger.info("✅ تم تهيئة وحدات التحليل بنجاح")
        else:
            logger.warning("⚠️ حدثت مشاكل في تهيئة وحدات التحليل")

        # تهيئة خدمة التنبيهات
        logger.info("🔔 جاري تهيئة خدمة التنبيهات...")
        initialize_alert_service(db, subscription_system, ca, user_states, get_text, show_main_menu, generate_stats_report)
        logger.info("✅ تم تهيئة خدمة التنبيهات بنجاح")

        # تهيئة خدمة إدارة API
        logger.info("🔑 جاري تهيئة خدمة إدارة API...")
        initialize_api_management_service(api_manager, subscription_system, user_states)
        logger.info("✅ تم تهيئة خدمة إدارة API بنجاح")

        # تهيئة خدمة إدارة المستخدمين
        logger.info("👥 جاري تهيئة خدمة إدارة المستخدمين...")
        initialize_user_management(
            db,
            subscription_system,
            api_manager,
            encryption_key,
            free_day_system,
            user_states,
            show_main_menu,
            show_language_selection,
            show_terms_and_conditions,
            initialize_system_extended,  # استخدام النسخة الموسعة
            SystemConfig,
            get_or_create_encryption_key,
            APIManager,
            get_text
        )
        logger.info("✅ تم تهيئة خدمة إدارة المستخدمين بنجاح")



        # إرجاع جميع الكائنات المهيأة وحفظها في الذاكرة المؤقتة
        # تهيئة نظام التخزين المؤقت
        from utils.firestore_cache import FirestoreCache
        firestore_cache = FirestoreCache(db)
        
        system_components = {
            'encryption_key': encryption_key,
            'api_manager': api_manager,
            'binance_verifier': binance_verifier,
            'auto_verifier': auto_verifier,
            'enhanced_analyzer': enhanced_analyzer,
            'subscription_system': subscription_system,
            'firestore_cache': firestore_cache  # إضافة كائن التخزين المؤقت
        }

        # تهيئة الأنظمة المحسنة الجديدة - الإصدار 4.4.0 (اختيارية)
        logger.info("🚀 جاري تهيئة الأنظمة المحسنة...")
        try:
            enhanced_systems = await initialize_enhanced_systems(db, api_manager, enhanced_analyzer)
            if enhanced_systems:
                logger.info("✅ تم تهيئة الأنظمة المحسنة بنجاح")
                # إضافة الأنظمة المحسنة للمكونات
                system_components.update(enhanced_systems)
            else:
                logger.warning("⚠️ فشل في تهيئة الأنظمة المحسنة - سيتم المتابعة بدونها")
        except Exception as enhanced_error:
            logger.warning(f"⚠️ خطأ في تهيئة الأنظمة المحسنة: {str(enhanced_error)}")
            logger.info("ℹ️ سيتم المتابعة بدون الأنظمة المحسنة")

        # تهيئة وحدة الدردشة مع الذكاء الاصطناعي
        logger.info("🤖 جاري تهيئة وحدة الدردشة مع الذكاء الاصطناعي...")
        try:
            import ai_chat
            ai_chat.initialize(api_manager)
            logger.info("✅ تم تهيئة وحدة الدردشة مع الذكاء الاصطناعي بنجاح")
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة وحدة الدردشة مع الذكاء الاصطناعي: {str(e)}")

        logger.info("✅ تم تهيئة النظام بنجاح")

        # حفظ المكونات في الذاكرة المؤقتة وتعيين علامة التهيئة
        _system_components_cache = system_components
        _extended_system_initialized = True

        # تأكيد نهائي لحالة التهيئة
        logger.info(f"🎉 تم تأكيد تهيئة النظام الموسع - علامة التهيئة: {_extended_system_initialized}")
        logger.info(f"📦 عدد المكونات المُهيأة: {len(system_components)}")
        logger.info(f"🔍 حالة التهيئة النهائية: {is_system_initialized()}")

        return system_components
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة النظام: {str(e)}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")

        # في حالة الفشل، نحاول إرجاع المكونات الأساسية على الأقل
        logger.info("🔄 محاولة إرجاع المكونات الأساسية...")
        try:
            basic_components = {}
            if 'encryption_key' in locals() and encryption_key:
                basic_components['encryption_key'] = encryption_key
            if 'api_manager' in locals() and api_manager:
                basic_components['api_manager'] = api_manager
            if 'subscription_system' in locals() and subscription_system:
                basic_components['subscription_system'] = subscription_system

            if len(basic_components) >= 3:  # المكونات الأساسية الثلاثة
                _system_components_cache = basic_components
                _extended_system_initialized = True
                logger.info("✅ تم إرجاع المكونات الأساسية بنجاح")
                logger.info(f"🔍 حالة التهيئة الأساسية: {is_system_initialized()}")
                return basic_components
            else:
                logger.error("❌ فشل في الحصول على المكونات الأساسية")
        except Exception as basic_error:
            logger.error(f"❌ خطأ في إرجاع المكونات الأساسية: {str(basic_error)}")

        return None

def get_system_components():
    """الحصول على المكونات المُهيأة للنظام"""
    global _extended_system_initialized, _system_components_cache

    if _extended_system_initialized and _system_components_cache:
        return _system_components_cache
    return None

def is_system_initialized():
    """التحقق من حالة تهيئة النظام"""
    global _extended_system_initialized, _system_components_cache
    return _extended_system_initialized and _system_components_cache is not None

def reset_system_initialization():
    """إعادة تعيين حالة التهيئة (للاختبار فقط)"""
    global _extended_system_initialized, _system_components_cache

    _extended_system_initialized = False
    _system_components_cache = None
    logger.info("🔄 تم إعادة تعيين حالة تهيئة النظام")

def force_system_initialization(components):
    """إجبار تعيين حالة التهيئة مع المكونات المحددة"""
    global _extended_system_initialized, _system_components_cache

    if components and isinstance(components, dict):
        _system_components_cache = components
        _extended_system_initialized = True
        logger.info("✅ تم إجبار تعيين حالة التهيئة")
        logger.info(f"🔍 حالة التهيئة الحالية - مُهيأ: {_extended_system_initialized}, مكونات: {len(components)} عنصر")
        return True
    else:
        logger.warning(f"⚠️ فشل في إجبار تعيين حالة التهيئة - مكونات غير صالحة: {type(components)}")
        return False

async def initialize_enhanced_systems(db, api_manager, enhanced_analyzer):
    """تهيئة الأنظمة المحسنة الجديدة - الإصدار 4.4.0"""
    enhanced_components = {}

    try:
        logger.info("🚀 جاري تهيئة الأنظمة المحسنة الجديدة...")

        # تهيئة المؤشرات المحسنة
        try:
            logger.info("📊 تهيئة المؤشرات المحسنة...")
            from analysis.optimized_indicators import OptimizedIndicators
            optimized_indicators = OptimizedIndicators()
            enhanced_components['optimized_indicators'] = optimized_indicators
            logger.info("✅ تم تهيئة المؤشرات المحسنة")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة المؤشرات المحسنة: {str(e)}")

        # تهيئة المحلل المحسن للذكاء الاصطناعي
        try:
            logger.info("🤖 تهيئة المحلل المحسن للذكاء الاصطناعي...")
            from analysis.enhanced_ai_analysis import initialize_enhanced_ai_analyzer
            enhanced_ai_analyzer = initialize_enhanced_ai_analyzer(api_manager, db)
            await enhanced_ai_analyzer.initialize()
            enhanced_components['enhanced_ai_analyzer'] = enhanced_ai_analyzer
            logger.info("✅ تم تهيئة المحلل المحسن للذكاء الاصطناعي")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة المحلل المحسن للذكاء الاصطناعي: {str(e)}")

        # تهيئة الملفات البديلة الفارغة (الاستضافة تتولى هذه المهام)
        try:
            logger.info("📈 تهيئة مراقب الأداء البديل...")
            from monitoring.real_time_performance import RealTimePerformanceMonitor
            performance_monitor = RealTimePerformanceMonitor()
            await performance_monitor.start_monitoring()
            enhanced_components['performance_monitor'] = performance_monitor
            logger.info("✅ تم تهيئة مراقب الأداء البديل (فارغ)")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة مراقب الأداء البديل: {str(e)}")

        try:
            logger.info("🧠 تهيئة مدير الذاكرة البديل...")
            from utils.memory_manager import AdvancedMemoryManager
            memory_manager = AdvancedMemoryManager()
            await memory_manager.start_monitoring()
            enhanced_components['memory_manager'] = memory_manager
            logger.info("✅ تم تهيئة مدير الذاكرة البديل (فارغ)")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة مدير الذاكرة البديل: {str(e)}")

        # تهيئة نظام الأمان
        try:
            logger.info("🛡️ تهيئة نظام الأمان...")
            from security.api_security import AdvancedAPISecurityManager
            api_security = AdvancedAPISecurityManager(db)
            await api_security.initialize_security_rules()
            enhanced_components['api_security'] = api_security
            logger.info("✅ تم تهيئة نظام الأمان")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة نظام الأمان: {str(e)}")

        # تهيئة قاعدة البيانات المحسنة
        try:
            logger.info("🗄️ تهيئة قاعدة البيانات المحسنة...")
            from database.optimized_queries import OptimizedFirestoreManager
            optimized_db = OptimizedFirestoreManager(db)
            enhanced_components['optimized_db'] = optimized_db
            logger.info("✅ تم تهيئة قاعدة البيانات المحسنة")
        except Exception as e:
            logger.warning(f"⚠️ فشل في تهيئة قاعدة البيانات المحسنة: {str(e)}")

        if enhanced_components:
            logger.info(f"✅ تم تهيئة {len(enhanced_components)} من الأنظمة المحسنة بنجاح")
            return enhanced_components
        else:
            logger.warning("⚠️ فشل في تهيئة جميع الأنظمة المحسنة")
            return None

    except Exception as e:
        logger.error(f"❌ خطأ عام في تهيئة الأنظمة المحسنة: {str(e)}")
        import traceback
        logger.error(f"تفاصيل الخطأ: {traceback.format_exc()}")
        return enhanced_components if enhanced_components else None

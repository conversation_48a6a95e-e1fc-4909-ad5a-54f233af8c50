"""
📈 محلل السوق المستمر
===================

نظام تحليل مستمر لأسواق العملات الرقمية باستخدام المؤشرات الفنية
والذكاء الاصطناعي لاكتشاف الفرص والإشارات.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import requests

logger = logging.getLogger(__name__)

@dataclass
class MarketSignal:
    """إشارة السوق"""
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    strength: float  # 0.0 - 1.0
    confidence: float  # 0.0 - 1.0
    timeframe: str
    indicators: Dict[str, Any]
    price: float
    timestamp: datetime
    reasoning: str

@dataclass
class TechnicalIndicators:
    """المؤشرات الفنية"""
    rsi: float
    macd: float
    macd_signal: float
    bb_upper: float
    bb_lower: float
    bb_middle: float
    ema_20: float
    ema_50: float
    sma_200: float
    volume_ratio: float
    support_level: float
    resistance_level: float

class MarketAnalyzer:
    """محلل السوق المتقدم"""
    
    def __init__(self, api_manager=None):
        """
        تهيئة محلل السوق
        
        Args:
            api_manager: مدير API للحصول على بيانات السوق
        """
        self.api_manager = api_manager
        
        # العملات المراقبة (العملات الرئيسية المتوافقة شرعياً)
        self.monitored_symbols = [
            'BTC', 'ETH', 'BNB', 'ADA', 'DOT', 'LINK',
            'LTC', 'BCH', 'XLM', 'VET', 'ALGO', 'ATOM',
            'SOL', 'AVAX', 'MATIC', 'FTM', 'NEAR', 'ICP'
        ]
        
        # الإطارات الزمنية للتحليل
        self.timeframes = ['1h', '4h', '1d']
        
        # عتبات الإشارات
        self.signal_thresholds = {
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'volume_spike': 2.0,  # ضعف المتوسط
            'breakout_threshold': 0.02  # 2% اختراق
        }
    
    async def analyze_market_continuously(self) -> List[MarketSignal]:
        """
        تحليل السوق بشكل مستمر
        
        Returns:
            قائمة الإشارات المكتشفة
        """
        signals = []
        
        try:
            for symbol in self.monitored_symbols:
                for timeframe in self.timeframes:
                    try:
                        # الحصول على بيانات السوق
                        market_data = await self._get_market_data(symbol, timeframe)
                        
                        if market_data:
                            # تحليل البيانات
                            signal = await self._analyze_symbol(symbol, market_data, timeframe)
                            
                            if signal and signal.strength > 0.6:  # إشارات قوية فقط
                                signals.append(signal)
                                logger.info(f"إشارة {signal.signal_type} قوية للعملة {symbol} في الإطار {timeframe}")
                    
                    except Exception as e:
                        logger.error(f"خطأ في تحليل {symbol} في الإطار {timeframe}: {str(e)}")
                        continue
                
                # تأخير قصير لتجنب الضغط على API
                await asyncio.sleep(0.1)
        
        except Exception as e:
            logger.error(f"خطأ في التحليل المستمر للسوق: {str(e)}")
        
        return signals
    
    async def _get_market_data(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات السوق
        
        Args:
            symbol: رمز العملة
            timeframe: الإطار الزمني
            
        Returns:
            بيانات السوق أو None
        """
        try:
            # استخدام CoinGecko API للحصول على البيانات
            url = f"https://api.coingecko.com/api/v3/coins/{symbol.lower()}"
            params = {
                'localization': 'false',
                'tickers': 'false',
                'market_data': 'true',
                'community_data': 'false',
                'developer_data': 'false',
                'sparkline': 'false'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # استخراج البيانات المطلوبة
                market_data = {
                    'symbol': symbol,
                    'current_price': data['market_data']['current_price']['usd'],
                    'market_cap': data['market_data']['market_cap']['usd'],
                    'total_volume': data['market_data']['total_volume']['usd'],
                    'price_change_24h': data['market_data']['price_change_percentage_24h'],
                    'price_change_7d': data['market_data']['price_change_percentage_7d'],
                    'market_cap_rank': data['market_data']['market_cap_rank'],
                    'circulating_supply': data['market_data']['circulating_supply'],
                    'total_supply': data['market_data']['total_supply'],
                    'high_24h': data['market_data']['high_24h']['usd'],
                    'low_24h': data['market_data']['low_24h']['usd'],
                    'timestamp': datetime.now()
                }
                
                # الحصول على البيانات التاريخية للمؤشرات
                historical_data = await self._get_historical_data(symbol, timeframe)
                if historical_data:
                    market_data['historical'] = historical_data
                
                return market_data
            
            else:
                logger.warning(f"فشل في الحصول على بيانات {symbol}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على بيانات السوق لـ {symbol}: {str(e)}")
            return None
    
    async def _get_historical_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        الحصول على البيانات التاريخية
        
        Args:
            symbol: رمز العملة
            timeframe: الإطار الزمني
            
        Returns:
            DataFrame مع البيانات التاريخية
        """
        try:
            # تحديد عدد الأيام بناءً على الإطار الزمني
            days_map = {'1h': 2, '4h': 7, '1d': 30}
            days = days_map.get(timeframe, 7)
            
            url = f"https://api.coingecko.com/api/v3/coins/{symbol.lower()}/market_chart"
            params = {
                'vs_currency': 'usd',
                'days': days,
                'interval': 'hourly' if timeframe in ['1h', '4h'] else 'daily'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # تحويل البيانات إلى DataFrame
                prices = data['prices']
                volumes = data['total_volumes']
                
                df = pd.DataFrame(prices, columns=['timestamp', 'price'])
                df['volume'] = [v[1] for v in volumes]
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                
                return df
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على البيانات التاريخية لـ {symbol}: {str(e)}")
            return None
    
    async def _analyze_symbol(self, symbol: str, market_data: Dict[str, Any], timeframe: str) -> Optional[MarketSignal]:
        """
        تحليل عملة محددة
        
        Args:
            symbol: رمز العملة
            market_data: بيانات السوق
            timeframe: الإطار الزمني
            
        Returns:
            إشارة التداول أو None
        """
        try:
            # حساب المؤشرات الفنية
            indicators = await self._calculate_technical_indicators(market_data)
            
            if not indicators:
                return None
            
            # تحليل الإشارات
            signals = []
            reasoning_parts = []
            
            # تحليل RSI
            if indicators.rsi < self.signal_thresholds['rsi_oversold']:
                signals.append(('buy', 0.7))
                reasoning_parts.append(f"RSI منخفض ({indicators.rsi:.1f}) - إشارة شراء")
            elif indicators.rsi > self.signal_thresholds['rsi_overbought']:
                signals.append(('sell', 0.7))
                reasoning_parts.append(f"RSI مرتفع ({indicators.rsi:.1f}) - إشارة بيع")
            
            # تحليل MACD
            if indicators.macd > indicators.macd_signal:
                signals.append(('buy', 0.6))
                reasoning_parts.append("MACD إيجابي - اتجاه صاعد")
            else:
                signals.append(('sell', 0.6))
                reasoning_parts.append("MACD سلبي - اتجاه هابط")
            
            # تحليل Bollinger Bands
            current_price = market_data['current_price']
            if current_price <= indicators.bb_lower:
                signals.append(('buy', 0.8))
                reasoning_parts.append("السعر عند الحد السفلي لنطاق بولينجر - إشارة شراء قوية")
            elif current_price >= indicators.bb_upper:
                signals.append(('sell', 0.8))
                reasoning_parts.append("السعر عند الحد العلوي لنطاق بولينجر - إشارة بيع قوية")
            
            # تحليل المتوسطات المتحركة
            if indicators.ema_20 > indicators.ema_50:
                signals.append(('buy', 0.5))
                reasoning_parts.append("المتوسط المتحرك قصير المدى أعلى من طويل المدى")
            else:
                signals.append(('sell', 0.5))
                reasoning_parts.append("المتوسط المتحرك قصير المدى أقل من طويل المدى")
            
            # تحليل الحجم
            if indicators.volume_ratio > self.signal_thresholds['volume_spike']:
                # زيادة في الحجم تعزز الإشارة
                for i, (signal_type, strength) in enumerate(signals):
                    signals[i] = (signal_type, min(strength * 1.2, 1.0))
                reasoning_parts.append(f"حجم تداول مرتفع ({indicators.volume_ratio:.1f}x) - تأكيد الإشارة")
            
            # تجميع الإشارات
            if not signals:
                return None
            
            # حساب الإشارة النهائية
            buy_signals = [s for s in signals if s[0] == 'buy']
            sell_signals = [s for s in signals if s[0] == 'sell']
            
            if len(buy_signals) > len(sell_signals):
                signal_type = 'buy'
                strength = sum(s[1] for s in buy_signals) / len(buy_signals)
            elif len(sell_signals) > len(buy_signals):
                signal_type = 'sell'
                strength = sum(s[1] for s in sell_signals) / len(sell_signals)
            else:
                signal_type = 'hold'
                strength = 0.5
            
            # حساب الثقة
            confidence = min(abs(len(buy_signals) - len(sell_signals)) / len(signals), 1.0)
            
            return MarketSignal(
                symbol=symbol,
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                timeframe=timeframe,
                indicators=indicators.__dict__,
                price=current_price,
                timestamp=datetime.now(),
                reasoning=" | ".join(reasoning_parts)
            )
            
        except Exception as e:
            logger.error(f"خطأ في تحليل العملة {symbol}: {str(e)}")
            return None
    
    async def _calculate_technical_indicators(self, market_data: Dict[str, Any]) -> Optional[TechnicalIndicators]:
        """
        حساب المؤشرات الفنية
        
        Args:
            market_data: بيانات السوق
            
        Returns:
            المؤشرات الفنية المحسوبة
        """
        try:
            historical_data = market_data.get('historical')
            if historical_data is None or len(historical_data) < 20:
                # استخدام قيم افتراضية إذا لم تتوفر بيانات كافية
                current_price = market_data['current_price']
                return TechnicalIndicators(
                    rsi=50.0,  # محايد
                    macd=0.0,
                    macd_signal=0.0,
                    bb_upper=current_price * 1.02,
                    bb_lower=current_price * 0.98,
                    bb_middle=current_price,
                    ema_20=current_price,
                    ema_50=current_price,
                    sma_200=current_price,
                    volume_ratio=1.0,
                    support_level=market_data['low_24h'],
                    resistance_level=market_data['high_24h']
                )
            
            df = historical_data.copy()
            
            # حساب RSI
            rsi = self._calculate_rsi(df['price'])
            
            # حساب MACD
            macd, macd_signal = self._calculate_macd(df['price'])
            
            # حساب Bollinger Bands
            bb_upper, bb_lower, bb_middle = self._calculate_bollinger_bands(df['price'])
            
            # حساب المتوسطات المتحركة
            ema_20 = df['price'].ewm(span=20).mean().iloc[-1]
            ema_50 = df['price'].ewm(span=50).mean().iloc[-1] if len(df) >= 50 else ema_20
            sma_200 = df['price'].rolling(window=200).mean().iloc[-1] if len(df) >= 200 else ema_20
            
            # حساب نسبة الحجم
            avg_volume = df['volume'].rolling(window=20).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
            
            # حساب مستويات الدعم والمقاومة
            support_level = df['price'].rolling(window=20).min().iloc[-1]
            resistance_level = df['price'].rolling(window=20).max().iloc[-1]
            
            return TechnicalIndicators(
                rsi=rsi,
                macd=macd,
                macd_signal=macd_signal,
                bb_upper=bb_upper,
                bb_lower=bb_lower,
                bb_middle=bb_middle,
                ema_20=ema_20,
                ema_50=ema_50,
                sma_200=sma_200,
                volume_ratio=volume_ratio,
                support_level=support_level,
                resistance_level=resistance_level
            )
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات الفنية: {str(e)}")
            return None
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """حساب مؤشر القوة النسبية RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.iloc[-1]
        except:
            return 50.0  # قيمة محايدة
    
    def _calculate_macd(self, prices: pd.Series) -> Tuple[float, float]:
        """حساب مؤشر MACD"""
        try:
            ema_12 = prices.ewm(span=12).mean()
            ema_26 = prices.ewm(span=26).mean()
            macd = ema_12 - ema_26
            macd_signal = macd.ewm(span=9).mean()
            return macd.iloc[-1], macd_signal.iloc[-1]
        except:
            return 0.0, 0.0
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20) -> Tuple[float, float, float]:
        """حساب نطاقات بولينجر"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            bb_upper = sma + (std * 2)
            bb_lower = sma - (std * 2)
            return bb_upper.iloc[-1], bb_lower.iloc[-1], sma.iloc[-1]
        except:
            current_price = prices.iloc[-1]
            return current_price * 1.02, current_price * 0.98, current_price

    async def get_market_overview(self) -> Dict[str, Any]:
        """
        الحصول على نظرة عامة على السوق

        Returns:
            ملخص حالة السوق
        """
        try:
            overview = {
                'total_symbols': len(self.monitored_symbols),
                'bullish_signals': 0,
                'bearish_signals': 0,
                'neutral_signals': 0,
                'high_volume_symbols': [],
                'trending_symbols': [],
                'timestamp': datetime.now()
            }

            signals = await self.analyze_market_continuously()

            for signal in signals:
                if signal.signal_type == 'buy':
                    overview['bullish_signals'] += 1
                elif signal.signal_type == 'sell':
                    overview['bearish_signals'] += 1
                else:
                    overview['neutral_signals'] += 1

                # إضافة العملات ذات الحجم العالي
                if signal.indicators.get('volume_ratio', 1.0) > 2.0:
                    overview['high_volume_symbols'].append(signal.symbol)

            return overview

        except Exception as e:
            logger.error(f"خطأ في الحصول على نظرة عامة للسوق: {str(e)}")
            return {
                'total_symbols': 0,
                'bullish_signals': 0,
                'bearish_signals': 0,
                'neutral_signals': 0,
                'high_volume_symbols': [],
                'trending_symbols': [],
                'timestamp': datetime.now()
            }

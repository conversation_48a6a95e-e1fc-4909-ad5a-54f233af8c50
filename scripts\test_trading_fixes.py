#!/usr/bin/env python3
"""
🧪 سكربت اختبار إصلاحات نظام التداول
=====================================

اختبار شامل للإصلاحات المطبقة على:
1. Firebase indexes
2. Enhanced market data APIs
3. Telegram message updates

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.trading.enhanced_market_data import enhanced_market_data
from src.trading.market_analyzer import MarketAnalyzer
from src.trading.trading_db import TradingDatabase

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_trading_fixes.log')
    ]
)

logger = logging.getLogger(__name__)

class TradingSystemTester:
    """فئة اختبار نظام التداول"""
    
    def __init__(self):
        self.market_analyzer = MarketAnalyzer()
        self.db_manager = TradingDatabase()
        self.test_symbols = ['BTC', 'ETH', 'BNB', 'ADA', 'INVALID_SYMBOL']
        
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء اختبار إصلاحات نظام التداول")
        
        test_results = {
            'market_data_api': await self.test_market_data_api(),
            'rate_limiting': await self.test_rate_limiting(),
            'error_handling': await self.test_error_handling(),
            'firebase_queries': await self.test_firebase_queries(),
            'data_caching': await self.test_data_caching()
        }
        
        # عرض النتائج
        self.display_results(test_results)
        
        return test_results
    
    async def test_market_data_api(self):
        """اختبار APIs بيانات السوق المحسنة"""
        logger.info("🔍 اختبار APIs بيانات السوق...")
        
        results = {}
        
        for symbol in self.test_symbols[:3]:  # اختبار 3 رموز فقط
            try:
                start_time = datetime.now()
                data = await enhanced_market_data.get_market_data(symbol)
                end_time = datetime.now()
                
                response_time = (end_time - start_time).total_seconds()
                
                if data:
                    results[symbol] = {
                        'status': 'success',
                        'response_time': response_time,
                        'source': data.get('source', 'unknown'),
                        'price': data.get('current_price', 'N/A')
                    }
                    logger.info(f"✅ {symbol}: {data['source']} - ${data.get('current_price', 'N/A')} ({response_time:.2f}s)")
                else:
                    results[symbol] = {
                        'status': 'failed',
                        'response_time': response_time,
                        'error': 'No data returned'
                    }
                    logger.warning(f"❌ {symbol}: فشل في الحصول على البيانات")
                
            except Exception as e:
                results[symbol] = {
                    'status': 'error',
                    'error': str(e)
                }
                logger.error(f"💥 {symbol}: خطأ - {str(e)}")
        
        return results
    
    async def test_rate_limiting(self):
        """اختبار آلية rate limiting"""
        logger.info("⏱️ اختبار آلية rate limiting...")
        
        try:
            # إجراء عدة طلبات متتالية
            requests_count = 5
            start_time = datetime.now()
            
            for i in range(requests_count):
                data = await enhanced_market_data.get_market_data('BTC')
                logger.info(f"طلب {i+1}: {'نجح' if data else 'فشل'}")
                
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            # يجب أن يستغرق وقتاً أطول بسبب rate limiting
            expected_min_time = requests_count * 0.5  # 0.5 ثانية على الأقل بين الطلبات
            
            return {
                'status': 'success' if total_time >= expected_min_time else 'warning',
                'total_time': total_time,
                'requests_count': requests_count,
                'avg_time_per_request': total_time / requests_count
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    async def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        logger.info("🛡️ اختبار معالجة الأخطاء...")
        
        try:
            # اختبار رمز غير صحيح
            data = await enhanced_market_data.get_market_data('INVALID_SYMBOL')
            
            return {
                'status': 'success',
                'handled_gracefully': data is None,
                'message': 'تم التعامل مع الرمز غير الصحيح بشكل صحيح'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e),
                'message': 'لم يتم التعامل مع الخطأ بشكل صحيح'
            }
    
    async def test_firebase_queries(self):
        """اختبار استعلامات Firebase"""
        logger.info("🔥 اختبار استعلامات Firebase...")
        
        try:
            # اختبار استعلام التوصيات (يجب أن يعمل الآن مع الفهارس الجديدة)
            test_user_id = "test_user_123"
            
            # محاولة الحصول على التوصيات
            recommendations = await self.db_manager.get_user_recommendations(
                test_user_id, 
                status='active', 
                limit=5
            )
            
            return {
                'status': 'success',
                'query_executed': True,
                'recommendations_count': len(recommendations) if recommendations else 0,
                'message': 'استعلام Firebase نجح (الفهارس تعمل)'
            }
            
        except Exception as e:
            error_msg = str(e)
            if "index" in error_msg.lower():
                return {
                    'status': 'failed',
                    'error': error_msg,
                    'message': 'مشكلة في فهارس Firebase - تحقق من إنشاء الفهارس'
                }
            else:
                return {
                    'status': 'error',
                    'error': error_msg,
                    'message': 'خطأ آخر في Firebase'
                }
    
    async def test_data_caching(self):
        """اختبار التخزين المؤقت"""
        logger.info("💾 اختبار التخزين المؤقت...")
        
        try:
            symbol = 'BTC'
            
            # الطلب الأول (من API)
            start_time1 = datetime.now()
            data1 = await enhanced_market_data.get_market_data(symbol)
            end_time1 = datetime.now()
            time1 = (end_time1 - start_time1).total_seconds()
            
            # الطلب الثاني (من Cache)
            start_time2 = datetime.now()
            data2 = await enhanced_market_data.get_market_data(symbol)
            end_time2 = datetime.now()
            time2 = (end_time2 - start_time2).total_seconds()
            
            # الطلب الثاني يجب أن يكون أسرع (من التخزين المؤقت)
            cache_working = time2 < time1 and data1 and data2
            
            return {
                'status': 'success' if cache_working else 'warning',
                'first_request_time': time1,
                'second_request_time': time2,
                'cache_working': cache_working,
                'speed_improvement': f"{((time1 - time2) / time1 * 100):.1f}%" if time1 > 0 else "N/A"
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def display_results(self, results):
        """عرض نتائج الاختبارات"""
        logger.info("\n" + "="*60)
        logger.info("📊 نتائج اختبار إصلاحات نظام التداول")
        logger.info("="*60)
        
        for test_name, result in results.items():
            status_emoji = {
                'success': '✅',
                'warning': '⚠️',
                'failed': '❌',
                'error': '💥'
            }.get(result.get('status', 'unknown'), '❓')
            
            logger.info(f"\n{status_emoji} {test_name.upper()}:")
            
            for key, value in result.items():
                if key != 'status':
                    logger.info(f"   {key}: {value}")
        
        # ملخص عام
        success_count = sum(1 for r in results.values() if r.get('status') == 'success')
        total_tests = len(results)
        
        logger.info(f"\n🎯 الملخص: {success_count}/{total_tests} اختبارات نجحت")
        
        if success_count == total_tests:
            logger.info("🎉 جميع الإصلاحات تعمل بشكل صحيح!")
        else:
            logger.warning("⚠️ بعض الإصلاحات تحتاج مراجعة")

async def main():
    """الدالة الرئيسية"""
    tester = TradingSystemTester()
    
    try:
        results = await tester.run_all_tests()
        
        # حفظ النتائج في ملف
        import json
        with open('test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info("💾 تم حفظ النتائج في test_results.json")
        
    except Exception as e:
        logger.error(f"💥 خطأ في تشغيل الاختبارات: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(main())

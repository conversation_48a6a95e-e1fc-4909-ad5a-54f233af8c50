#!/usr/bin/env python3
"""
Manual Firebase Indexes Creation Guide
======================================

Generate direct links and instructions for creating Firebase indexes manually

Author: Augment Agent
"""

import json
import os
import sys
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def generate_console_links():
    """Generate direct Firebase Console links for index creation"""
    
    print("Firebase Indexes Manual Creation Guide")
    print("=" * 60)
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Your Firebase project ID (update this with your actual project ID)
    project_id = "tradingtelegram-da632"  # Update this!
    
    print(f"Project ID: {project_id}")
    print()
    
    # Base URLs
    console_base = f"https://console.firebase.google.com/project/{project_id}/firestore"
    indexes_url = f"{console_base}/indexes"
    
    print("Step 1: Open Firebase Console")
    print("-" * 30)
    print(f"🔗 Firebase Console: {console_base}")
    print(f"🔗 Indexes Section: {indexes_url}")
    print()
    
    print("Step 2: Create the following indexes manually")
    print("-" * 50)
    
    # Index configurations
    indexes = [
        {
            "name": "Main Trading Recommendations Index",
            "collection": "trading_recommendations",
            "fields": [
                {"field": "status", "order": "Ascending"},
                {"field": "user_id", "order": "Ascending"},
                {"field": "created_at", "order": "Descending"}
            ]
        },
        {
            "name": "User Recommendations Index", 
            "collection": "trading_recommendations",
            "fields": [
                {"field": "user_id", "order": "Ascending"},
                {"field": "created_at", "order": "Descending"}
            ]
        },
        {
            "name": "Symbol Recommendations Index",
            "collection": "trading_recommendations", 
            "fields": [
                {"field": "symbol", "order": "Ascending"},
                {"field": "created_at", "order": "Descending"}
            ]
        }
    ]
    
    for i, index in enumerate(indexes, 1):
        print(f"\nIndex {i}: {index['name']}")
        print(f"Collection: {index['collection']}")
        print("Fields:")
        for field in index['fields']:
            print(f"  - {field['field']} ({field['order']})")
        print()
    
    print("Step 3: Detailed Instructions")
    print("-" * 30)
    print("1. Go to Firebase Console (link above)")
    print("2. Select your project if not already selected")
    print("3. Navigate to 'Firestore Database' in the left menu")
    print("4. Click on 'Indexes' tab")
    print("5. Click 'Create Index' button")
    print("6. For each index above:")
    print("   a. Enter Collection ID: trading_recommendations")
    print("   b. Add fields as specified above")
    print("   c. Set the correct order (Ascending/Descending)")
    print("   d. Click 'Create'")
    print("7. Wait for all indexes to build (status will show 'Building' then 'Enabled')")
    print()
    
    return indexes

def create_test_script():
    """Create a simple test script to verify indexes work"""
    
    test_script = '''#!/usr/bin/env python3
"""
Test Firebase Indexes
=====================

Simple test to check if indexes are working
"""

import asyncio
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_indexes():
    """Test if Firebase indexes are working"""
    try:
        import firebase_admin
        from firebase_admin import credentials, firestore
        
        # Initialize Firebase (you may need to set up credentials)
        try:
            app = firebase_admin.get_app()
        except ValueError:
            # Initialize with default credentials or service account
            cred = credentials.ApplicationDefault()
            firebase_admin.initialize_app(cred)
        
        db = firestore.client()
        
        # Test the queries that require indexes
        collection_ref = db.collection('trading_recommendations')
        
        # Test query 1: user_id + status + created_at
        try:
            query1 = collection_ref.where('user_id', '==', 'test_user').where('status', '==', 'active').order_by('created_at', direction='DESCENDING').limit(1)
            docs1 = list(query1.stream())
            logger.info("✅ Index 1 (user_id + status + created_at): Working")
        except Exception as e:
            if 'index' in str(e).lower():
                logger.error("❌ Index 1: Missing")
            else:
                logger.warning(f"⚠️ Index 1: {str(e)}")
        
        # Test query 2: user_id + created_at
        try:
            query2 = collection_ref.where('user_id', '==', 'test_user').order_by('created_at', direction='DESCENDING').limit(1)
            docs2 = list(query2.stream())
            logger.info("✅ Index 2 (user_id + created_at): Working")
        except Exception as e:
            if 'index' in str(e).lower():
                logger.error("❌ Index 2: Missing")
            else:
                logger.warning(f"⚠️ Index 2: {str(e)}")
        
        # Test query 3: symbol + created_at
        try:
            query3 = collection_ref.where('symbol', '==', 'BTC').order_by('created_at', direction='DESCENDING').limit(1)
            docs3 = list(query3.stream())
            logger.info("✅ Index 3 (symbol + created_at): Working")
        except Exception as e:
            if 'index' in str(e).lower():
                logger.error("❌ Index 3: Missing")
            else:
                logger.warning(f"⚠️ Index 3: {str(e)}")
        
        logger.info("\\nTest completed!")
        
    except ImportError:
        logger.error("Firebase Admin SDK not installed: pip install firebase-admin")
    except Exception as e:
        logger.error(f"Error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_indexes())
'''
    
    # Save test script
    test_file = os.path.join(project_root, 'scripts', 'test_indexes_simple.py')
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    print(f"✅ Test script created: {test_file}")
    print("Run with: python scripts/test_indexes_simple.py")
    print()

def main():
    """Main function"""
    
    print("🔧 Firebase Indexes Manual Setup")
    print("=" * 60)
    print()
    
    # Generate console links and instructions
    indexes = generate_console_links()
    
    # Create test script
    create_test_script()
    
    print("Alternative: Use Firebase CLI (if you can install it)")
    print("-" * 55)
    print("1. Install Node.js from: https://nodejs.org/")
    print("2. Install Firebase CLI: npm install -g firebase-tools")
    print("3. Login: firebase login")
    print("4. Initialize: firebase init firestore")
    print("5. Deploy indexes: firebase deploy --only firestore:indexes")
    print()
    
    print("🎯 Summary")
    print("-" * 10)
    print("✅ Manual instructions generated")
    print("✅ Test script created")
    print("✅ Alternative CLI method provided")
    print()
    print("Choose the method that works best for you!")
    print("After creating indexes, run: python scripts/test_indexes_simple.py")

if __name__ == "__main__":
    main()

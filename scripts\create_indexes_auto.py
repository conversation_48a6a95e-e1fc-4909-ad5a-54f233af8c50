#!/usr/bin/env python3
"""
Automatic Firebase Indexes Creation
===================================

Create Firebase indexes automatically using Admin SDK

Author: Augment Agent
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FirebaseIndexCreator:
    """Firebase index creation class"""
    
    def __init__(self):
        self.indexes_created = []
        self.errors = []
        
    async def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            import firebase_admin
            from firebase_admin import credentials, firestore
            
            # Check if Firebase is already initialized
            try:
                app = firebase_admin.get_app()
                logger.info("Firebase already initialized")
            except ValueError:
                # Initialize Firebase
                try:
                    # Try to use service account key
                    cred = credentials.Certificate('path/to/serviceAccountKey.json')
                    firebase_admin.initialize_app(cred)
                    logger.info("Firebase initialized with service account")
                except Exception:
                    # Try to use default credentials
                    try:
                        cred = credentials.ApplicationDefault()
                        firebase_admin.initialize_app(cred)
                        logger.info("Firebase initialized with default credentials")
                    except Exception:
                        logger.warning("Could not initialize Firebase Admin SDK")
                        return False
            
            self.db = firestore.client()
            return True
            
        except ImportError:
            logger.error("Firebase Admin SDK not installed. Install with: pip install firebase-admin")
            return False
        except Exception as e:
            logger.error(f"Error initializing Firebase: {str(e)}")
            return False
    
    async def create_composite_indexes(self):
        """Create composite indexes for trading_recommendations"""
        logger.info("Creating composite indexes...")
        
        # Note: Composite indexes cannot be created programmatically via Admin SDK
        # They must be created via Firebase Console or Firebase CLI
        
        indexes_config = {
            "indexes": [
                {
                    "collectionGroup": "trading_recommendations",
                    "queryScope": "COLLECTION",
                    "fields": [
                        {"fieldPath": "status", "order": "ASCENDING"},
                        {"fieldPath": "user_id", "order": "ASCENDING"},
                        {"fieldPath": "created_at", "order": "DESCENDING"}
                    ]
                },
                {
                    "collectionGroup": "trading_recommendations", 
                    "queryScope": "COLLECTION",
                    "fields": [
                        {"fieldPath": "user_id", "order": "ASCENDING"},
                        {"fieldPath": "created_at", "order": "DESCENDING"}
                    ]
                },
                {
                    "collectionGroup": "trading_recommendations",
                    "queryScope": "COLLECTION", 
                    "fields": [
                        {"fieldPath": "symbol", "order": "ASCENDING"},
                        {"fieldPath": "created_at", "order": "DESCENDING"}
                    ]
                }
            ]
        }
        
        logger.warning("Composite indexes cannot be created programmatically via Admin SDK")
        logger.info("Use Firebase CLI instead:")
        logger.info("1. Install Firebase CLI: npm install -g firebase-tools")
        logger.info("2. Login: firebase login")
        logger.info("3. Deploy indexes: firebase deploy --only firestore:indexes")
        
        return indexes_config
    
    async def create_single_field_indexes(self):
        """Create single field indexes that can be created programmatically"""
        logger.info("Creating single field indexes...")
        
        try:
            # These are usually created automatically, but we can ensure they exist
            collection_ref = self.db.collection('trading_recommendations')
            
            # Create some test documents to trigger automatic index creation
            test_doc = {
                'status': 'active',
                'user_id': 'test_user',
                'symbol': 'BTC',
                'created_at': datetime.now(),
                'test_document': True
            }
            
            doc_ref = collection_ref.add(test_doc)
            logger.info(f"Created test document: {doc_ref[1].id}")
            
            # Delete the test document
            doc_ref[1].delete()
            logger.info("Deleted test document")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating single field indexes: {str(e)}")
            return False
    
    async def generate_firebase_cli_commands(self):
        """Generate Firebase CLI commands for index creation"""
        logger.info("Generating Firebase CLI commands...")
        
        commands = [
            "# Firebase CLI Commands for Index Creation",
            "# ========================================",
            "",
            "# 1. Install Firebase CLI (if not installed)",
            "npm install -g firebase-tools",
            "",
            "# 2. Login to Firebase",
            "firebase login",
            "",
            "# 3. Initialize Firebase in your project directory",
            "firebase init firestore",
            "",
            "# 4. Deploy the indexes",
            "firebase deploy --only firestore:indexes",
            "",
            "# Alternative: Deploy specific index file",
            "firebase deploy --only firestore:indexes --project YOUR_PROJECT_ID"
        ]
        
        # Save commands to file
        commands_file = os.path.join(project_root, 'firebase_commands.txt')
        with open(commands_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(commands))
        
        logger.info(f"Firebase CLI commands saved to: {commands_file}")
        return commands_file
    
    async def test_firestore_connection(self):
        """Test Firestore connection"""
        logger.info("Testing Firestore connection...")
        
        try:
            # Try to read from a collection
            collections = self.db.collections()
            collection_names = [col.id for col in collections]
            
            logger.info(f"Connected to Firestore. Found collections: {collection_names}")
            return True
            
        except Exception as e:
            logger.error(f"Error connecting to Firestore: {str(e)}")
            return False

async def main():
    """Main function"""
    logger.info("Starting automatic Firebase index creation...")
    
    creator = FirebaseIndexCreator()
    
    # Initialize Firebase
    firebase_ready = await creator.initialize_firebase()
    
    if firebase_ready:
        logger.info("Firebase initialized successfully")
        
        # Test connection
        connection_ok = await creator.test_firestore_connection()
        
        if connection_ok:
            # Create single field indexes
            await creator.create_single_field_indexes()
        
        # Create composite indexes (via CLI)
        await creator.create_composite_indexes()
    else:
        logger.warning("Firebase not initialized - proceeding with CLI method")
    
    # Generate CLI commands
    commands_file = await creator.generate_firebase_cli_commands()
    
    logger.info("\n" + "="*60)
    logger.info("FIREBASE INDEX CREATION SUMMARY")
    logger.info("="*60)
    
    if firebase_ready:
        logger.info("✅ Firebase Admin SDK initialized")
        logger.info("✅ Single field indexes processed")
    else:
        logger.warning("⚠️ Firebase Admin SDK not available")
    
    logger.info("✅ Composite index configuration ready")
    logger.info(f"✅ CLI commands generated: {commands_file}")
    
    logger.info("\nNext Steps:")
    logger.info("1. Install Firebase CLI: npm install -g firebase-tools")
    logger.info("2. Run: firebase login")
    logger.info("3. Run: firebase init firestore (in project directory)")
    logger.info("4. Run: firebase deploy --only firestore:indexes")
    
    logger.info("\nAlternative: Use Firebase Console")
    logger.info("1. Go to https://console.firebase.google.com/")
    logger.info("2. Select your project > Firestore > Indexes")
    logger.info("3. Create the indexes manually using the config file")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

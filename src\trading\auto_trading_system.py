"""
🤖 نظام التداول الآلي الرئيسي
============================

النظام المركزي لإدارة التداول الآلي بثلاث مراحل:
- المرحلة الأولى: نظام شبه آلي (توصيات فقط)
- المرحلة الثانية: تداول آلي محدود
- المرحلة الثالثة: تداول آلي كامل

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from trading.trading_db import TradingDatabaseManager, UserTradingSettings
from trading.risk_manager import RiskManager
from trading.market_analyzer import MarketAnalyzer
from trading.recommendation_engine import RecommendationEngine, RecommendationRequest

logger = logging.getLogger(__name__)

class TradingPhase(Enum):
    """مراحل التداول الآلي"""
    PHASE_1_RECOMMENDATIONS = "phase_1_recommendations"  # توصيات فقط
    PHASE_2_LIMITED_AUTO = "phase_2_limited_auto"        # تداول آلي محدود
    PHASE_3_FULL_AUTO = "phase_3_full_auto"              # تداول آلي كامل

class SystemStatus(Enum):
    """حالة النظام"""
    ACTIVE = "active"
    PAUSED = "paused"
    MAINTENANCE = "maintenance"
    EMERGENCY_STOP = "emergency_stop"

@dataclass
class TradingSystemConfig:
    """إعدادات نظام التداول"""
    current_phase: TradingPhase
    max_concurrent_trades: int
    emergency_stop_loss: float  # نسبة الخسارة لإيقاف النظام
    daily_loss_limit: float     # حد الخسارة اليومية
    analysis_interval: int      # فترة التحليل بالدقائق
    notification_enabled: bool
    islamic_compliance_only: bool

class AutoTradingSystem:
    """النظام الرئيسي للتداول الآلي"""
    
    def __init__(self, db_manager: TradingDatabaseManager, risk_manager: RiskManager,
                 market_analyzer: MarketAnalyzer, recommendation_engine: RecommendationEngine,
                 telegram_bot=None):
        """
        تهيئة نظام التداول الآلي
        
        Args:
            db_manager: مدير قاعدة البيانات
            risk_manager: مدير المخاطر
            market_analyzer: محلل السوق
            recommendation_engine: محرك التوصيات
            telegram_bot: بوت التليجرام للإشعارات
        """
        self.db_manager = db_manager
        self.risk_manager = risk_manager
        self.market_analyzer = market_analyzer
        self.recommendation_engine = recommendation_engine
        self.telegram_bot = telegram_bot
        
        # حالة النظام
        self.status = SystemStatus.ACTIVE
        self.current_phase = TradingPhase.PHASE_1_RECOMMENDATIONS
        
        # إعدادات النظام الافتراضية
        self.config = TradingSystemConfig(
            current_phase=TradingPhase.PHASE_1_RECOMMENDATIONS,
            max_concurrent_trades=5,
            emergency_stop_loss=0.10,  # 10% خسارة إجمالية
            daily_loss_limit=0.05,     # 5% خسارة يومية
            analysis_interval=15,      # كل 15 دقيقة
            notification_enabled=True,
            islamic_compliance_only=True
        )
        
        # متغيرات التشغيل
        self.active_recommendations = {}
        self.daily_pnl = 0.0
        self.total_pnl = 0.0
        self.last_analysis_time = None
        
        # مهام التشغيل
        self.analysis_task = None
        self.monitoring_task = None
        
        logger.info("تم تهيئة نظام التداول الآلي بنجاح")
    
    async def start_system(self) -> bool:
        """
        بدء تشغيل النظام
        
        Returns:
            True إذا تم البدء بنجاح
        """
        try:
            if self.status != SystemStatus.ACTIVE:
                logger.warning("النظام غير نشط، لا يمكن البدء")
                return False
            
            logger.info(f"بدء تشغيل نظام التداول الآلي - المرحلة: {self.current_phase.value}")
            
            # بدء مهام التحليل والمراقبة
            self.analysis_task = asyncio.create_task(self._continuous_analysis())
            self.monitoring_task = asyncio.create_task(self._system_monitoring())
            
            # إرسال إشعار البدء
            if self.telegram_bot and self.config.notification_enabled:
                await self._send_system_notification("🚀 تم بدء تشغيل نظام التداول الآلي")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء تشغيل النظام: {str(e)}")
            return False
    
    async def stop_system(self, reason: str = "إيقاف يدوي") -> bool:
        """
        إيقاف النظام
        
        Args:
            reason: سبب الإيقاف
            
        Returns:
            True إذا تم الإيقاف بنجاح
        """
        try:
            logger.info(f"إيقاف نظام التداول الآلي - السبب: {reason}")
            
            # إيقاف المهام
            if self.analysis_task:
                self.analysis_task.cancel()
            if self.monitoring_task:
                self.monitoring_task.cancel()
            
            # تحديث حالة النظام
            self.status = SystemStatus.PAUSED
            
            # إرسال إشعار الإيقاف
            if self.telegram_bot and self.config.notification_enabled:
                await self._send_system_notification(f"⏸️ تم إيقاف نظام التداول الآلي\nالسبب: {reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إيقاف النظام: {str(e)}")
            return False
    
    async def emergency_stop(self, reason: str = "إيقاف طارئ") -> bool:
        """
        إيقاف طارئ للنظام
        
        Args:
            reason: سبب الإيقاف الطارئ
            
        Returns:
            True إذا تم الإيقاف بنجاح
        """
        try:
            logger.critical(f"إيقاف طارئ لنظام التداول - السبب: {reason}")
            
            # تحديث حالة النظام
            self.status = SystemStatus.EMERGENCY_STOP
            
            # إيقاف جميع المهام
            await self.stop_system(f"إيقاف طارئ: {reason}")
            
            # إلغاء جميع التوصيات النشطة
            await self._cancel_all_active_recommendations()
            
            # إرسال تنبيه طارئ
            if self.telegram_bot:
                await self._send_emergency_notification(f"🚨 إيقاف طارئ للنظام!\n{reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في الإيقاف الطارئ: {str(e)}")
            return False
    
    async def _continuous_analysis(self):
        """مهمة التحليل المستمر"""
        while self.status == SystemStatus.ACTIVE:
            try:
                logger.debug("بدء دورة تحليل جديدة")
                
                # تحليل السوق
                market_signals = await self.market_analyzer.analyze_market_continuously()
                
                # معالجة الإشارات حسب المرحلة الحالية
                if self.current_phase == TradingPhase.PHASE_1_RECOMMENDATIONS:
                    await self._process_phase1_signals(market_signals)
                elif self.current_phase == TradingPhase.PHASE_2_LIMITED_AUTO:
                    await self._process_phase2_signals(market_signals)
                elif self.current_phase == TradingPhase.PHASE_3_FULL_AUTO:
                    await self._process_phase3_signals(market_signals)
                
                # تحديث وقت التحليل الأخير
                self.last_analysis_time = datetime.now()
                
                # انتظار الفترة التالية
                await asyncio.sleep(self.config.analysis_interval * 60)
                
            except asyncio.CancelledError:
                logger.info("تم إلغاء مهمة التحليل المستمر")
                break
            except Exception as e:
                logger.error(f"خطأ في التحليل المستمر: {str(e)}")
                await asyncio.sleep(60)  # انتظار دقيقة قبل المحاولة مرة أخرى
    
    async def _process_phase1_signals(self, signals: List[Any]):
        """معالجة إشارات المرحلة الأولى (توصيات فقط)"""
        try:
            # الحصول على المستخدمين النشطين
            active_users = await self._get_active_users()
            
            for user_id in active_users:
                # الحصول على إعدادات المستخدم
                user_settings = await self.db_manager.get_user_settings(user_id)
                if not user_settings or not user_settings.enabled:
                    continue
                
                # إنشاء طلب توصيات
                request = RecommendationRequest(
                    user_id=user_id,
                    symbols=user_settings.preferred_symbols or ['BTC', 'ETH', 'BNB'],
                    risk_tolerance=user_settings.risk_tolerance,
                    investment_amount=user_settings.max_position_size,
                    timeframe='medium',
                    islamic_compliance=user_settings.islamic_compliance
                )
                
                # إنشاء التوصيات
                recommendations = await self.recommendation_engine.generate_recommendations(request)
                
                # إرسال التوصيات للمستخدم
                if recommendations and self.telegram_bot:
                    await self._send_recommendations_to_user(user_id, recommendations)
                
        except Exception as e:
            logger.error(f"خطأ في معالجة إشارات المرحلة الأولى: {str(e)}")
    
    async def _process_phase2_signals(self, signals: List[Any]):
        """معالجة إشارات المرحلة الثانية (تداول آلي محدود)"""
        try:
            # المرحلة الثانية - تداول آلي محدود
            # سيتم تطويرها في المستقبل
            logger.info("المرحلة الثانية قيد التطوير")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة إشارات المرحلة الثانية: {str(e)}")
    
    async def _process_phase3_signals(self, signals: List[Any]):
        """معالجة إشارات المرحلة الثالثة (تداول آلي كامل)"""
        try:
            # المرحلة الثالثة - تداول آلي كامل
            # سيتم تطويرها في المستقبل
            logger.info("المرحلة الثالثة قيد التطوير")
            
        except Exception as e:
            logger.error(f"خطأ في معالجة إشارات المرحلة الثالثة: {str(e)}")
    
    async def _system_monitoring(self):
        """مهمة مراقبة النظام"""
        while self.status == SystemStatus.ACTIVE:
            try:
                # فحص حالة النظام
                await self._check_system_health()
                
                # تنظيف التوصيات منتهية الصلاحية
                await self.db_manager.cleanup_expired_recommendations()
                
                # فحص حدود الخسارة
                await self._check_loss_limits()
                
                # انتظار 5 دقائق قبل الفحص التالي
                await asyncio.sleep(300)
                
            except asyncio.CancelledError:
                logger.info("تم إلغاء مهمة مراقبة النظام")
                break
            except Exception as e:
                logger.error(f"خطأ في مراقبة النظام: {str(e)}")
                await asyncio.sleep(60)
    
    async def _check_system_health(self):
        """فحص صحة النظام"""
        try:
            # فحص آخر وقت تحليل
            if self.last_analysis_time:
                time_since_analysis = datetime.now() - self.last_analysis_time
                if time_since_analysis > timedelta(minutes=self.config.analysis_interval * 2):
                    logger.warning("تأخر في دورة التحليل")
            
            # فحص اتصال قاعدة البيانات
            active_users_count = await self.db_manager.get_active_users_count()
            logger.debug(f"عدد المستخدمين النشطين: {active_users_count}")
            
        except Exception as e:
            logger.error(f"خطأ في فحص صحة النظام: {str(e)}")
    
    async def _check_loss_limits(self):
        """فحص حدود الخسارة"""
        try:
            # فحص الخسارة اليومية
            if abs(self.daily_pnl) > self.config.daily_loss_limit:
                await self.emergency_stop(f"تجاوز حد الخسارة اليومية: {self.daily_pnl:.2%}")
            
            # فحص الخسارة الإجمالية
            if abs(self.total_pnl) > self.config.emergency_stop_loss:
                await self.emergency_stop(f"تجاوز حد الخسارة الإجمالية: {self.total_pnl:.2%}")
                
        except Exception as e:
            logger.error(f"خطأ في فحص حدود الخسارة: {str(e)}")
    
    async def _get_active_users(self) -> List[str]:
        """الحصول على قائمة المستخدمين النشطين"""
        try:
            # هذه الوظيفة تحتاج إلى تطوير في قاعدة البيانات
            # مؤقتاً نعيد قائمة فارغة
            return []
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدمين النشطين: {str(e)}")
            return []
    
    async def _send_recommendations_to_user(self, user_id: str, recommendations: List[Any]):
        """إرسال التوصيات للمستخدم"""
        try:
            if not self.telegram_bot:
                return
            
            # تنسيق رسالة التوصيات
            message = "🎯 *توصيات التداول الجديدة*\n\n"
            
            for i, rec in enumerate(recommendations[:3], 1):  # أول 3 توصيات فقط
                message += f"*{i}. {rec.symbol}*\n"
                message += f"📊 العملية: {rec.action.upper()}\n"
                message += f"💰 السعر: ${rec.entry_price:.4f}\n"
                message += f"🎯 الهدف: ${rec.target_price:.4f}\n"
                message += f"🛡️ وقف الخسارة: ${rec.stop_loss:.4f}\n"
                message += f"⭐ الثقة: {rec.confidence:.1%}\n"
                message += f"⚠️ المخاطرة: {rec.risk_level}\n\n"
            
            message += "⚡ هذه توصيات آلية، يرجى إجراء تحليلك الخاص قبل التداول"
            
            # إرسال الرسالة
            await self.telegram_bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"خطأ في إرسال التوصيات للمستخدم {user_id}: {str(e)}")
    
    async def _send_system_notification(self, message: str):
        """إرسال إشعار النظام"""
        try:
            # إرسال للمطورين أو المديرين
            # يمكن تخصيص قائمة المستقبلين
            pass
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار النظام: {str(e)}")
    
    async def _send_emergency_notification(self, message: str):
        """إرسال تنبيه طارئ"""
        try:
            # إرسال تنبيه طارئ للمطورين
            logger.critical(message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال التنبيه الطارئ: {str(e)}")
    
    async def _cancel_all_active_recommendations(self):
        """إلغاء جميع التوصيات النشطة"""
        try:
            # تحديث حالة جميع التوصيات النشطة إلى ملغاة
            # سيتم تطوير هذه الوظيفة في قاعدة البيانات
            logger.info("تم إلغاء جميع التوصيات النشطة")
            
        except Exception as e:
            logger.error(f"خطأ في إلغاء التوصيات النشطة: {str(e)}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        return {
            'status': self.status.value,
            'phase': self.current_phase.value,
            'last_analysis': self.last_analysis_time.isoformat() if self.last_analysis_time else None,
            'daily_pnl': self.daily_pnl,
            'total_pnl': self.total_pnl,
            'active_recommendations': len(self.active_recommendations),
            'config': {
                'analysis_interval': self.config.analysis_interval,
                'max_concurrent_trades': self.config.max_concurrent_trades,
                'emergency_stop_loss': self.config.emergency_stop_loss,
                'daily_loss_limit': self.config.daily_loss_limit
            }
        }

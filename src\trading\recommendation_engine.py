"""
🎯 محرك التوصيات الذكي
====================

نظام متقدم لإنشاء توصيات التداول باستخدام الذكاء الاصطناعي
والتحليل الفني مع مراعاة إدارة المخاطر والامتثال الشرعي.

المؤلف: Augment Agent
الإصدار: 1.0.0
"""

import logging
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from trading.trading_db import TradingRecommendation, TradingDatabaseManager
from trading.risk_manager import RiskManager, RiskAssessment
from trading.market_analyzer import MarketAnalyzer, MarketSignal

logger = logging.getLogger(__name__)

@dataclass
class RecommendationRequest:
    """طلب إنشاء توصية"""
    user_id: str
    symbols: List[str]  # العملات المطلوب تحليلها
    risk_tolerance: str  # 'conservative', 'moderate', 'aggressive'
    investment_amount: float
    timeframe: str  # 'short', 'medium', 'long'
    islamic_compliance: bool

class RecommendationEngine:
    """محرك التوصيات الذكي"""
    
    def __init__(self, db_manager: TradingDatabaseManager, 
                 risk_manager: RiskManager, market_analyzer: MarketAnalyzer,
                 gemini_client=None):
        """
        تهيئة محرك التوصيات
        
        Args:
            db_manager: مدير قاعدة البيانات
            risk_manager: مدير المخاطر
            market_analyzer: محلل السوق
            gemini_client: عميل Gemini AI
        """
        self.db_manager = db_manager
        self.risk_manager = risk_manager
        self.market_analyzer = market_analyzer
        self.gemini_client = gemini_client
        
        # إعدادات التوصيات
        self.recommendation_settings = {
            'conservative': {
                'max_risk_score': 0.4,
                'min_confidence': 0.7,
                'position_size_ratio': 0.05,  # 5% من رأس المال
                'stop_loss_ratio': 0.03,      # 3% وقف خسارة
                'take_profit_ratio': 0.06     # 6% جني أرباح
            },
            'moderate': {
                'max_risk_score': 0.6,
                'min_confidence': 0.6,
                'position_size_ratio': 0.08,  # 8% من رأس المال
                'stop_loss_ratio': 0.05,      # 5% وقف خسارة
                'take_profit_ratio': 0.10     # 10% جني أرباح
            },
            'aggressive': {
                'max_risk_score': 0.8,
                'min_confidence': 0.5,
                'position_size_ratio': 0.12,  # 12% من رأس المال
                'stop_loss_ratio': 0.08,      # 8% وقف خسارة
                'take_profit_ratio': 0.15     # 15% جني أرباح
            }
        }
    
    async def generate_recommendations(self, request: RecommendationRequest) -> List[TradingRecommendation]:
        """
        إنشاء توصيات التداول
        
        Args:
            request: طلب التوصيات
            
        Returns:
            قائمة التوصيات المُنشأة
        """
        recommendations = []
        
        try:
            logger.info(f"بدء إنشاء توصيات للمستخدم {request.user_id}")
            
            # الحصول على إعدادات المخاطرة
            risk_settings = self.recommendation_settings.get(request.risk_tolerance, 
                                                           self.recommendation_settings['moderate'])
            
            # تحليل كل عملة
            for symbol in request.symbols:
                try:
                    recommendation = await self._analyze_symbol_for_recommendation(
                        symbol, request, risk_settings
                    )
                    
                    if recommendation:
                        recommendations.append(recommendation)
                        logger.info(f"تم إنشاء توصية {recommendation.action} للعملة {symbol}")
                
                except Exception as e:
                    logger.error(f"خطأ في تحليل العملة {symbol}: {str(e)}")
                    continue
            
            # ترتيب التوصيات حسب الثقة
            recommendations.sort(key=lambda x: x.confidence, reverse=True)
            
            # حفظ التوصيات في قاعدة البيانات
            for recommendation in recommendations:
                await self.db_manager.save_recommendation(recommendation)
            
            logger.info(f"تم إنشاء {len(recommendations)} توصية للمستخدم {request.user_id}")
            return recommendations
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصيات: {str(e)}")
            return []
    
    async def _analyze_symbol_for_recommendation(self, symbol: str, request: RecommendationRequest,
                                               risk_settings: Dict[str, Any]) -> Optional[TradingRecommendation]:
        """
        تحليل عملة محددة لإنشاء توصية
        
        Args:
            symbol: رمز العملة
            request: طلب التوصيات
            risk_settings: إعدادات المخاطرة
            
        Returns:
            التوصية المُنشأة أو None
        """
        try:
            # الحصول على بيانات السوق
            market_data = await self.market_analyzer._get_market_data(symbol, '1d')
            if not market_data:
                logger.warning(f"لا توجد بيانات سوق للعملة {symbol}")
                return None
            
            # تقييم المخاطر
            risk_assessment = self.risk_manager.assess_trading_risk(
                symbol, market_data, 
                request.investment_amount * risk_settings['position_size_ratio'],
                request.investment_amount
            )
            
            # فحص الامتثال الشرعي
            if request.islamic_compliance and risk_assessment.compliance_status.value != 'compliant':
                logger.info(f"العملة {symbol} غير متوافقة مع الأحكام الشرعية")
                return None
            
            # فحص مستوى المخاطرة
            if risk_assessment.risk_score > risk_settings['max_risk_score']:
                logger.info(f"العملة {symbol} تتجاوز حد المخاطرة المقبول")
                return None
            
            # تحليل الإشارات الفنية
            signal = await self.market_analyzer._analyze_symbol(symbol, market_data, '1d')
            if not signal:
                logger.info(f"لا توجد إشارات واضحة للعملة {symbol}")
                return None
            
            # فحص قوة الإشارة
            if signal.confidence < risk_settings['min_confidence']:
                logger.info(f"إشارة العملة {symbol} ضعيفة ({signal.confidence:.2f})")
                return None
            
            # تحديد نوع التوصية
            if signal.signal_type == 'hold':
                return None  # لا نُنشئ توصيات للانتظار
            
            # حساب الأسعار المستهدفة
            current_price = market_data['current_price']
            
            if signal.signal_type == 'buy':
                entry_price = current_price
                target_price = current_price * (1 + risk_settings['take_profit_ratio'])
                stop_loss = current_price * (1 - risk_settings['stop_loss_ratio'])
            else:  # sell
                entry_price = current_price
                target_price = current_price * (1 - risk_settings['take_profit_ratio'])
                stop_loss = current_price * (1 + risk_settings['stop_loss_ratio'])
            
            # إنشاء التحليل بالذكاء الاصطناعي
            ai_analysis = await self._generate_ai_analysis(symbol, market_data, signal, risk_assessment)
            
            # تحديد مدة انتهاء الصلاحية
            expiry_hours = {'short': 24, 'medium': 72, 'long': 168}  # أسبوع للطويل
            expires_at = datetime.now() + timedelta(hours=expiry_hours.get(request.timeframe, 72))
            
            # إنشاء التوصية
            recommendation = TradingRecommendation(
                id=str(uuid.uuid4()),
                user_id=request.user_id,
                symbol=symbol,
                action=signal.signal_type,
                confidence=signal.confidence,
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                risk_level=risk_assessment.risk_level.value,
                reasoning=signal.reasoning,
                created_at=datetime.now(),
                expires_at=expires_at,
                status='active',
                ai_analysis=ai_analysis,
                technical_indicators=signal.indicators
            )
            
            return recommendation
            
        except Exception as e:
            logger.error(f"خطأ في تحليل العملة {symbol} للتوصية: {str(e)}")
            return None
    
    async def _generate_ai_analysis(self, symbol: str, market_data: Dict[str, Any],
                                  signal: MarketSignal, risk_assessment: RiskAssessment) -> str:
        """
        إنشاء تحليل بالذكاء الاصطناعي
        
        Args:
            symbol: رمز العملة
            market_data: بيانات السوق
            signal: الإشارة الفنية
            risk_assessment: تقييم المخاطر
            
        Returns:
            التحليل النصي
        """
        try:
            if not self.gemini_client:
                return self._generate_basic_analysis(symbol, market_data, signal, risk_assessment)
            
            # إعداد البيانات للذكاء الاصطناعي
            prompt = f"""
            قم بتحليل العملة الرقمية {symbol} وإعطاء توصية تداول مفصلة:

            بيانات السوق:
            - السعر الحالي: ${market_data['current_price']:.4f}
            - التغيير 24 ساعة: {market_data['price_change_24h']:.2f}%
            - القيمة السوقية: ${market_data['market_cap']:,.0f}
            - الحجم: ${market_data['total_volume']:,.0f}
            - الترتيب: #{market_data['market_cap_rank']}

            الإشارة الفنية:
            - نوع الإشارة: {signal.signal_type}
            - القوة: {signal.strength:.2f}
            - الثقة: {signal.confidence:.2f}
            - السبب: {signal.reasoning}

            تقييم المخاطر:
            - مستوى المخاطرة: {risk_assessment.risk_level.value}
            - نتيجة المخاطر: {risk_assessment.risk_score:.2f}
            - حالة الامتثال الشرعي: {risk_assessment.compliance_status.value}

            اكتب تحليلاً شاملاً باللغة العربية يتضمن:
            1. تحليل الوضع الحالي للعملة
            2. الأسباب الفنية للتوصية
            3. المخاطر المحتملة
            4. نصائح إدارة المخاطر
            5. التوقعات قصيرة ومتوسطة المدى

            يجب أن يكون التحليل مفيداً ومفهوماً للمتداولين.
            """
            
            # استدعاء Gemini AI
            response = await self.gemini_client.generate_content_async(prompt)
            
            if response and response.text:
                return response.text.strip()
            else:
                return self._generate_basic_analysis(symbol, market_data, signal, risk_assessment)
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء التحليل بالذكاء الاصطناعي: {str(e)}")
            return self._generate_basic_analysis(symbol, market_data, signal, risk_assessment)
    
    def _generate_basic_analysis(self, symbol: str, market_data: Dict[str, Any],
                               signal: MarketSignal, risk_assessment: RiskAssessment) -> str:
        """
        إنشاء تحليل أساسي بدون ذكاء اصطناعي
        
        Args:
            symbol: رمز العملة
            market_data: بيانات السوق
            signal: الإشارة الفنية
            risk_assessment: تقييم المخاطر
            
        Returns:
            التحليل النصي الأساسي
        """
        analysis_parts = []
        
        # تحليل الوضع العام
        price_change = market_data['price_change_24h']
        if price_change > 5:
            analysis_parts.append(f"📈 العملة {symbol} تشهد ارتفاعاً قوياً بنسبة {price_change:.1f}% خلال 24 ساعة.")
        elif price_change > 0:
            analysis_parts.append(f"📊 العملة {symbol} تسجل ارتفاعاً طفيفاً بنسبة {price_change:.1f}%.")
        elif price_change > -5:
            analysis_parts.append(f"📉 العملة {symbol} تشهد انخفاضاً طفيفاً بنسبة {abs(price_change):.1f}%.")
        else:
            analysis_parts.append(f"🔻 العملة {symbol} تواجه ضغوط بيع قوية مع انخفاض {abs(price_change):.1f}%.")
        
        # تحليل الإشارة الفنية
        if signal.signal_type == 'buy':
            analysis_parts.append(f"🎯 المؤشرات الفنية تشير إلى فرصة شراء بقوة {signal.strength:.1f} وثقة {signal.confidence:.1f}.")
        else:
            analysis_parts.append(f"⚠️ المؤشرات الفنية تشير إلى إشارة بيع بقوة {signal.strength:.1f} وثقة {signal.confidence:.1f}.")
        
        analysis_parts.append(f"📋 السبب الفني: {signal.reasoning}")
        
        # تحليل المخاطر
        risk_level_ar = {
            'low': 'منخفضة',
            'medium': 'متوسطة', 
            'high': 'عالية',
            'extreme': 'عالية جداً'
        }
        
        analysis_parts.append(f"🛡️ مستوى المخاطرة: {risk_level_ar.get(risk_assessment.risk_level.value, 'غير محدد')}")
        
        # إضافة التحذيرات
        if risk_assessment.warnings:
            analysis_parts.append("⚠️ تحذيرات:")
            for warning in risk_assessment.warnings:
                analysis_parts.append(f"  • {warning}")
        
        # إضافة التوصيات
        if risk_assessment.recommendations:
            analysis_parts.append("💡 توصيات إدارة المخاطر:")
            for recommendation in risk_assessment.recommendations:
                analysis_parts.append(f"  • {recommendation}")
        
        return "\n".join(analysis_parts)
    
    async def update_recommendation_status(self, recommendation_id: str, new_status: str) -> bool:
        """
        تحديث حالة التوصية
        
        Args:
            recommendation_id: معرف التوصية
            new_status: الحالة الجديدة
            
        Returns:
            True إذا تم التحديث بنجاح
        """
        try:
            # هذه الوظيفة تحتاج إلى تطوير في قاعدة البيانات
            # يمكن إضافتها لاحقاً
            logger.info(f"تحديث حالة التوصية {recommendation_id} إلى {new_status}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث حالة التوصية: {str(e)}")
            return False

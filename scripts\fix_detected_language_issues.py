#!/usr/bin/env python3
"""
سكريبت لإصلاح المشاكل المكتشفة في اللغة
يعالج التناقضات بين حقول lang و language
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('language_fix_targeted.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

class TargetedLanguageFixer:
    """فئة لإصلاح المشاكل المحددة في اللغة"""
    
    def __init__(self):
        self.stats = {
            'users_processed': 0,
            'users_fixed': 0,
            'inconsistencies_fixed': 0,
            'subscription_entries_created': 0,
            'errors': 0
        }
        self.db = None
        self.firebase_available = False
        
    def initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            from integrations.firebase_init import initialize_firebase
            self.db = initialize_firebase()
            if self.db:
                self.firebase_available = True
                logger.info("✅ تم تهيئة Firebase بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في تهيئة Firebase")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Firebase: {str(e)}")
            return False
    
    def determine_preferred_language(self, user_settings_data: dict, users_data: dict) -> str:
        """تحديد اللغة المفضلة للمستخدم بناءً على البيانات المتوفرة"""
        
        # أولوية 1: إذا كان lang و language متطابقين في user_settings
        if user_settings_data:
            lang_field = user_settings_data.get('lang')
            language_field = user_settings_data.get('language')
            
            if lang_field and language_field and lang_field == language_field:
                return lang_field
            
            # أولوية 2: إذا كان lang موجود ومختلف عن language، نفضل lang (أحدث)
            if lang_field and lang_field in ['ar', 'en']:
                return lang_field
            
            # أولوية 3: استخدام language إذا كان صالحاً
            if language_field and language_field in ['ar', 'en']:
                return language_field
        
        # أولوية 4: فحص users collection
        if users_data:
            language_field = users_data.get('language')
            if language_field and language_field in ['ar', 'en']:
                return language_field
        
        # افتراضي: العربية
        return 'ar'
    
    async def fix_user_language_consistency(self, user_id: str) -> dict:
        """إصلاح تناسق لغة مستخدم محدد"""
        if not self.firebase_available:
            return {'success': False, 'error': 'Firebase غير متوفر'}
        
        try:
            result = {
                'success': False,
                'user_id': user_id,
                'changes_made': [],
                'preferred_language': None,
                'had_inconsistency': False
            }
            
            # جلب البيانات من user_settings
            user_settings_data = {}
            try:
                settings_ref = self.db.collection('user_settings').document(user_id)
                settings_doc = settings_ref.get()
                if settings_doc.exists:
                    user_settings_data = settings_doc.to_dict()
            except Exception as e:
                logger.error(f"خطأ في جلب user_settings للمستخدم {user_id}: {str(e)}")
            
            # جلب البيانات من users
            users_data = {}
            try:
                users_ref = self.db.collection('users').document(user_id)
                users_doc = users_ref.get()
                if users_doc.exists:
                    users_data = users_doc.to_dict()
            except Exception as e:
                logger.error(f"خطأ في جلب users للمستخدم {user_id}: {str(e)}")
            
            # تحديد اللغة المفضلة
            preferred_lang = self.determine_preferred_language(user_settings_data, users_data)
            result['preferred_language'] = preferred_lang
            
            # فحص التناقضات
            inconsistencies = []
            
            if user_settings_data:
                lang_val = user_settings_data.get('lang')
                language_val = user_settings_data.get('language')
                
                if lang_val and language_val and lang_val != language_val:
                    inconsistencies.append(f"user_settings: lang={lang_val}, language={language_val}")
                    result['had_inconsistency'] = True
            
            if users_data:
                users_lang = users_data.get('language')
                if user_settings_data and user_settings_data.get('lang') != users_lang:
                    inconsistencies.append(f"users.language={users_lang} != user_settings.lang={user_settings_data.get('lang')}")
                    result['had_inconsistency'] = True
            
            if inconsistencies:
                logger.info(f"🔧 إصلاح تناقضات للمستخدم {user_id}: {', '.join(inconsistencies)}")
                self.stats['inconsistencies_fixed'] += 1
            
            # تحديث user_settings
            if user_settings_data:
                try:
                    settings_ref = self.db.collection('user_settings').document(user_id)
                    update_data = {
                        'lang': preferred_lang,
                        'language': preferred_lang,  # توحيد الحقلين
                        'updated_at': datetime.now()
                    }
                    settings_ref.update(update_data)
                    result['changes_made'].append(f"user_settings: lang={preferred_lang}, language={preferred_lang}")
                    logger.debug(f"تم تحديث user_settings للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث user_settings للمستخدم {user_id}: {str(e)}")
            
            # تحديث users
            if users_data:
                try:
                    users_ref = self.db.collection('users').document(user_id)
                    update_data = {
                        'lang': preferred_lang,  # إضافة حقل lang
                        'language': preferred_lang,
                        'updated_at': datetime.now()
                    }
                    users_ref.update(update_data)
                    result['changes_made'].append(f"users: lang={preferred_lang}, language={preferred_lang}")
                    logger.debug(f"تم تحديث users للمستخدم {user_id}")
                except Exception as e:
                    logger.error(f"خطأ في تحديث users للمستخدم {user_id}: {str(e)}")
            
            # إنشاء مدخل في subscription_system إذا لم يكن موجوداً
            try:
                subscription_ref = self.db.collection('subscription_system').document(user_id)
                subscription_doc = subscription_ref.get()
                
                if not subscription_doc.exists:
                    # إنشاء مدخل جديد
                    subscription_data = {
                        'user_id': user_id,
                        'lang': preferred_lang,
                        'active': True,
                        'created_at': datetime.now(),
                        'updated_at': datetime.now(),
                        'general_notifications': True,
                        'trading_signals': True
                    }
                    
                    # إضافة telegram_id إذا كان متوفراً
                    if users_data and 'telegram_id' in users_data:
                        subscription_data['telegram_id'] = users_data['telegram_id']
                    
                    subscription_ref.set(subscription_data)
                    result['changes_made'].append(f"subscription_system: created with lang={preferred_lang}")
                    self.stats['subscription_entries_created'] += 1
                    logger.info(f"تم إنشاء مدخل subscription_system للمستخدم {user_id}")
                else:
                    # تحديث المدخل الموجود
                    subscription_ref.update({
                        'lang': preferred_lang,
                        'updated_at': datetime.now()
                    })
                    result['changes_made'].append(f"subscription_system: updated lang={preferred_lang}")
                    logger.debug(f"تم تحديث subscription_system للمستخدم {user_id}")
                    
            except Exception as e:
                logger.error(f"خطأ في معالجة subscription_system للمستخدم {user_id}: {str(e)}")
            
            if result['changes_made']:
                result['success'] = True
                self.stats['users_fixed'] += 1
                logger.info(f"✅ تم إصلاح المستخدم {user_id} - اللغة المفضلة: {preferred_lang}")
            
            return result
            
        except Exception as e:
            logger.error(f"خطأ عام في إصلاح المستخدم {user_id}: {str(e)}")
            self.stats['errors'] += 1
            return {'success': False, 'error': str(e)}
    
    async def fix_all_detected_issues(self):
        """إصلاح جميع المشاكل المكتشفة"""
        logger.info("🔧 بدء إصلاح المشاكل المكتشفة في اللغة...")
        
        if not self.initialize_firebase():
            logger.error("❌ لا يمكن المتابعة بدون Firebase")
            return
        
        # الحصول على جميع المستخدمين من user_settings
        try:
            user_settings_docs = self.db.collection('user_settings').stream()
            users_to_fix = []
            
            for doc in user_settings_docs:
                users_to_fix.append(doc.id)
            
            logger.info(f"تم العثور على {len(users_to_fix)} مستخدم للمعالجة")
            
            # معالجة كل مستخدم
            results = []
            for user_id in users_to_fix:
                self.stats['users_processed'] += 1
                logger.info(f"معالجة المستخدم {user_id} ({self.stats['users_processed']}/{len(users_to_fix)})")
                
                result = await self.fix_user_language_consistency(user_id)
                results.append(result)
                
                if result['success']:
                    print(f"✅ {user_id}: {', '.join(result['changes_made'])}")
                else:
                    print(f"❌ {user_id}: {result.get('error', 'فشل غير محدد')}")
            
            # حفظ تقرير مفصل
            self.save_detailed_report(results)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة المستخدمين: {str(e)}")
            self.stats['errors'] += 1
        
        # طباعة الإحصائيات النهائية
        self.print_final_stats()
    
    def save_detailed_report(self, results: list):
        """حفظ تقرير مفصل للنتائج"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'stats': self.stats,
                'results': results
            }
            
            report_file = f"language_fix_detailed_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"📄 تم حفظ التقرير المفصل في: {report_file}")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التقرير: {str(e)}")
    
    def print_final_stats(self):
        """طباعة الإحصائيات النهائية"""
        print("\n" + "="*60)
        print("📊 الإحصائيات النهائية:")
        print(f"   👥 المستخدمين المعالجين: {self.stats['users_processed']}")
        print(f"   ✅ المستخدمين المُصلحين: {self.stats['users_fixed']}")
        print(f"   🔄 التناقضات المُصلحة: {self.stats['inconsistencies_fixed']}")
        print(f"   ➕ مدخلات subscription_system المُنشأة: {self.stats['subscription_entries_created']}")
        print(f"   ❌ الأخطاء: {self.stats['errors']}")
        print("="*60)

async def main():
    """الدالة الرئيسية"""
    print("🎯 بدء إصلاح المشاكل المكتشفة في اللغة")
    print("=" * 60)
    
    fixer = TargetedLanguageFixer()
    
    try:
        await fixer.fix_all_detected_issues()
        print("\n✅ تم إكمال الإصلاح بنجاح!")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الإصلاح بواسطة المستخدم")
        fixer.print_final_stats()
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الإصلاح: {str(e)}")
        logger.error(f"خطأ عام: {str(e)}")
        fixer.print_final_stats()

if __name__ == "__main__":
    asyncio.run(main())

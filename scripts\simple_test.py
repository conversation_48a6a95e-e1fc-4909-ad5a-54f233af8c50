#!/usr/bin/env python3
"""
🧪 اختبار مبسط للإصلاحات
========================

اختبار سريع للتأكد من عمل الإصلاحات الأساسية

المؤلف: Augment Agent
"""

import asyncio
import aiohttp
import logging
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_coingecko_api():
    """اختبار CoinGecko API مع rate limiting"""
    logger.info("🔍 اختبار CoinGecko API...")
    
    try:
        url = "https://api.coingecko.com/api/v3/coins/bitcoin"
        params = {
            'localization': 'false',
            'tickers': 'false',
            'market_data': 'true',
            'community_data': 'false',
            'developer_data': 'false',
            'sparkline': 'false'
        }
        
        timeout = aiohttp.ClientTimeout(total=15)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            start_time = datetime.now()
            async with session.get(url, params=params) as response:
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds()
                
                if response.status == 200:
                    data = await response.json()
                    price = data['market_data']['current_price']['usd']
                    logger.info(f"✅ CoinGecko: BTC = ${price} ({response_time:.2f}s)")
                    return True
                else:
                    logger.error(f"❌ CoinGecko error: {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"💥 CoinGecko خطأ: {str(e)}")
        return False

async def test_binance_api():
    """اختبار Binance API"""
    logger.info("🔍 اختبار Binance API...")
    
    try:
        url = "https://api.binance.com/api/v3/ticker/24hr"
        params = {'symbol': 'BTCUSDT'}
        
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            start_time = datetime.now()
            async with session.get(url, params=params) as response:
                end_time = datetime.now()
                response_time = (end_time - start_time).total_seconds()
                
                if response.status == 200:
                    data = await response.json()
                    price = float(data['lastPrice'])
                    logger.info(f"✅ Binance: BTC = ${price} ({response_time:.2f}s)")
                    return True
                else:
                    logger.error(f"❌ Binance error: {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"💥 Binance خطأ: {str(e)}")
        return False

async def test_rate_limiting():
    """اختبار rate limiting"""
    logger.info("⏱️ اختبار rate limiting...")
    
    try:
        # إجراء 3 طلبات متتالية
        start_time = datetime.now()
        
        for i in range(3):
            await asyncio.sleep(0.5)  # تأخير بسيط
            success = await test_coingecko_api()
            if not success:
                logger.warning(f"فشل الطلب {i+1}")
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ Rate limiting: {total_time:.2f}s لـ 3 طلبات")
        return total_time > 1.5  # يجب أن يستغرق وقتاً بسبب rate limiting
        
    except Exception as e:
        logger.error(f"💥 Rate limiting خطأ: {str(e)}")
        return False

async def test_error_handling():
    """اختبار معالجة الأخطاء"""
    logger.info("🛡️ اختبار معالجة الأخطاء...")
    
    try:
        # اختبار URL غير صحيح
        url = "https://api.coingecko.com/api/v3/coins/invalid_coin_name"
        
        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url) as response:
                if response.status == 404:
                    logger.info("✅ معالجة خطأ 404 بنجاح")
                    return True
                else:
                    logger.warning(f"⚠️ استجابة غير متوقعة: {response.status}")
                    return False
                    
    except Exception as e:
        logger.info(f"✅ تم التعامل مع الخطأ: {str(e)}")
        return True

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء الاختبارات المبسطة...")
    
    tests = [
        ("CoinGecko API", test_coingecko_api),
        ("Binance API", test_binance_api),
        ("Rate Limiting", test_rate_limiting),
        ("Error Handling", test_error_handling)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            logger.info(f"{status} {test_name}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"💥 {test_name}: {str(e)}")
    
    # ملخص النتائج
    logger.info("\n" + "="*50)
    logger.info("📊 ملخص نتائج الاختبارات:")
    logger.info("="*50)
    
    success_count = 0
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        logger.info(f"{status} {test_name}")
        if result:
            success_count += 1
    
    total_tests = len(results)
    logger.info(f"\n🎯 النتيجة النهائية: {success_count}/{total_tests} اختبارات نجحت")
    
    if success_count == total_tests:
        logger.info("🎉 جميع الاختبارات نجحت!")
        return True
    else:
        logger.warning("⚠️ بعض الاختبارات فشلت")
        return False

if __name__ == "__main__":
    asyncio.run(main())

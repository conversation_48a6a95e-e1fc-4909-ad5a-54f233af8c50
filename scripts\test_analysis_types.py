#!/usr/bin/env python3
"""
🧪 اختبار أنواع التحليل المختلفة
==================================

اختبار شامل لدعم أنواع التحليل الثلاثة:
- التحليل الأساسي (basic)
- التحليل المحسن (enhanced) 
- تحليل الذكاء الاصطناعي (ai)

المؤلف: Augment Agent
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AnalysisTypeTester:
    """فئة اختبار أنواع التحليل"""
    
    def __init__(self):
        self.test_user_ids = {
            'free_user': 'test_free_123',
            'subscribed_user': 'test_sub_456', 
            'ai_user': 'test_ai_789'
        }
        self.test_symbols = ['BTC', 'ETH', 'BNB']
        
    async def test_user_access_levels(self):
        """اختبار مستويات الوصول للمستخدمين"""
        logger.info("🔍 اختبار مستويات الوصول للمستخدمين...")
        
        try:
            # محاولة استيراد النظام
            from handlers.trading_handlers import TradingHandlers
            
            # إنشاء مثيل للاختبار
            handler = TradingHandlers(None, None)
            
            results = {}
            
            for user_type, user_id in self.test_user_ids.items():
                try:
                    analysis_type = await handler._get_user_analysis_type(user_id)
                    analysis_info = handler._get_analysis_type_info(analysis_type)
                    
                    results[user_type] = {
                        'user_id': user_id,
                        'analysis_type': analysis_type,
                        'analysis_name': analysis_info['name'],
                        'features': analysis_info['features']
                    }
                    
                    logger.info(f"✅ {user_type}: {analysis_type}")
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في اختبار {user_type}: {str(e)}")
                    results[user_type] = {'error': str(e)}
            
            return results
            
        except ImportError as e:
            logger.error(f"❌ خطأ في الاستيراد: {str(e)}")
            return {'error': 'فشل في استيراد النظام'}
    
    async def test_market_data_access(self):
        """اختبار الوصول لبيانات السوق حسب نوع التحليل"""
        logger.info("📊 اختبار الوصول لبيانات السوق...")
        
        try:
            from trading.enhanced_market_data import enhanced_market_data
            
            results = {}
            analysis_types = ['basic', 'enhanced', 'ai']
            
            for analysis_type in analysis_types:
                type_results = {}
                
                for symbol in self.test_symbols:
                    try:
                        # اختبار الحصول على البيانات
                        data = await enhanced_market_data.get_market_data(
                            symbol, '1d', 'test_user', analysis_type
                        )
                        
                        if data:
                            type_results[symbol] = {
                                'success': True,
                                'source': data.get('source', 'unknown'),
                                'analysis_type': data.get('analysis_type', 'unknown'),
                                'has_enhanced_features': 'enhanced_features' in data
                            }
                        else:
                            type_results[symbol] = {'success': False, 'reason': 'no_data'}
                            
                    except Exception as e:
                        type_results[symbol] = {'success': False, 'error': str(e)}
                
                results[analysis_type] = type_results
                logger.info(f"✅ اختبار {analysis_type} مكتمل")
            
            return results
            
        except ImportError as e:
            logger.error(f"❌ خطأ في استيراد enhanced_market_data: {str(e)}")
            return {'error': 'فشل في استيراد نظام البيانات'}
    
    async def test_subscription_integration(self):
        """اختبار تكامل نظام الاشتراك"""
        logger.info("🔐 اختبار تكامل نظام الاشتراك...")
        
        try:
            from services.subscription_system import subscription_system
            
            results = {}
            
            for user_type, user_id in self.test_user_ids.items():
                try:
                    # اختبار التحقق من الاشتراك
                    subscription = await subscription_system.is_subscribed(user_id, full_details=True)
                    
                    # اختبار إمكانية استخدام التحليل المجاني
                    can_use_free = subscription_system.can_use_free_analysis(user_id)
                    
                    results[user_type] = {
                        'subscription_status': subscription,
                        'can_use_free_analysis': can_use_free,
                        'user_id': user_id
                    }
                    
                    logger.info(f"✅ {user_type}: اشتراك = {subscription}")
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في اختبار اشتراك {user_type}: {str(e)}")
                    results[user_type] = {'error': str(e)}
            
            return results
            
        except ImportError as e:
            logger.error(f"❌ خطأ في استيراد subscription_system: {str(e)}")
            return {'error': 'فشل في استيراد نظام الاشتراك'}
    
    async def test_api_management(self):
        """اختبار نظام إدارة API"""
        logger.info("🔑 اختبار نظام إدارة API...")
        
        try:
            from api_manager import APIManager
            api_manager = APIManager()
            
            results = {}
            
            for user_type, user_id in self.test_user_ids.items():
                try:
                    # اختبار التحقق من وجود مفاتيح API
                    has_gemini = await api_manager.has_api_keys(user_id, 'gemini')
                    has_binance = await api_manager.has_api_keys(user_id, 'binance')
                    
                    results[user_type] = {
                        'has_gemini_api': has_gemini,
                        'has_binance_api': has_binance,
                        'user_id': user_id
                    }
                    
                    logger.info(f"✅ {user_type}: Gemini = {has_gemini}, Binance = {has_binance}")
                    
                except Exception as e:
                    logger.error(f"❌ خطأ في اختبار API {user_type}: {str(e)}")
                    results[user_type] = {'error': str(e)}
            
            return results
            
        except ImportError as e:
            logger.error(f"❌ خطأ في استيراد api_manager: {str(e)}")
            return {'error': 'فشل في استيراد مدير API'}
    
    def print_results(self, test_name: str, results: dict):
        """طباعة نتائج الاختبار بشكل منظم"""
        logger.info(f"\n{'='*50}")
        logger.info(f"📋 نتائج {test_name}")
        logger.info(f"{'='*50}")
        
        if 'error' in results:
            logger.error(f"❌ خطأ عام: {results['error']}")
            return
        
        for key, value in results.items():
            logger.info(f"\n🔸 {key}:")
            if isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    logger.info(f"   • {sub_key}: {sub_value}")
            else:
                logger.info(f"   {value}")

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء اختبار أنواع التحليل المختلفة...")
    
    tester = AnalysisTypeTester()
    
    # قائمة الاختبارات
    tests = [
        ("مستويات الوصول للمستخدمين", tester.test_user_access_levels),
        ("الوصول لبيانات السوق", tester.test_market_data_access),
        ("تكامل نظام الاشتراك", tester.test_subscription_integration),
        ("نظام إدارة API", tester.test_api_management)
    ]
    
    all_results = {}
    success_count = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n🔄 تشغيل اختبار: {test_name}")
            results = await test_func()
            
            all_results[test_name] = results
            tester.print_results(test_name, results)
            
            if 'error' not in results:
                success_count += 1
                logger.info(f"✅ {test_name} نجح")
            else:
                logger.error(f"❌ {test_name} فشل")
                
        except Exception as e:
            logger.error(f"💥 خطأ في تشغيل {test_name}: {str(e)}")
            all_results[test_name] = {'error': str(e)}
    
    # ملخص النتائج النهائية
    logger.info(f"\n{'='*60}")
    logger.info("🎯 ملخص النتائج النهائية")
    logger.info(f"{'='*60}")
    logger.info(f"✅ اختبارات نجحت: {success_count}/{len(tests)}")
    logger.info(f"❌ اختبارات فشلت: {len(tests) - success_count}/{len(tests)}")
    
    if success_count == len(tests):
        logger.info("🎉 جميع الاختبارات نجحت! النظام يدعم أنواع التحليل المختلفة بشكل صحيح.")
        return True
    else:
        logger.warning("⚠️ بعض الاختبارات فشلت. يحتاج النظام لمراجعة.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
System Verification Script
==========================

Verify that all system components are in place and working

Author: Augment Agent
"""

import os
import sys
from datetime import datetime

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def check_file_exists(file_path):
    """Check if a file exists"""
    full_path = os.path.join(project_root, file_path)
    exists = os.path.exists(full_path)
    print(f"{'✅' if exists else '❌'} {file_path}")
    return exists

def check_directory_exists(dir_path):
    """Check if a directory exists"""
    full_path = os.path.join(project_root, dir_path)
    exists = os.path.exists(full_path) and os.path.isdir(full_path)
    print(f"{'✅' if exists else '❌'} {dir_path}/")
    return exists

def main():
    """Main verification function"""
    print("System Verification Report")
    print("=" * 50)
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Project Root: {project_root}")
    print()
    
    # Check core directories
    print("📁 Core Directories:")
    dirs_to_check = [
        'src',
        'src/trading',
        'src/handlers',
        'src/services',
        'src/analysis',
        'src/config',
        'scripts'
    ]
    
    dir_results = []
    for directory in dirs_to_check:
        result = check_directory_exists(directory)
        dir_results.append(result)
    
    print()
    
    # Check core files
    print("📄 Core Files:")
    files_to_check = [
        'src/trading/enhanced_market_data.py',
        'src/trading/market_analyzer.py',
        'src/handlers/trading_handlers.py',
        'src/services/subscription_system.py',
        'src/analysis/analysis_helpers.py',
        'src/api_manager.py',
        'src/config/firestore.indexes.json',
        'scripts/setup_firebase.py',
        'scripts/test_integration.py'
    ]
    
    file_results = []
    for file_path in files_to_check:
        result = check_file_exists(file_path)
        file_results.append(result)
    
    print()
    
    # Check configuration files
    print("⚙️ Configuration Files:")
    config_files = [
        'requirements.txt',
        'config.py',
        '.env.example'
    ]
    
    config_results = []
    for config_file in config_files:
        result = check_file_exists(config_file)
        config_results.append(result)
    
    print()
    
    # Summary
    print("📊 Summary:")
    print(f"Directories: {sum(dir_results)}/{len(dir_results)} found")
    print(f"Core Files: {sum(file_results)}/{len(file_results)} found")
    print(f"Config Files: {sum(config_results)}/{len(config_results)} found")
    
    total_checks = len(dir_results) + len(file_results) + len(config_results)
    total_passed = sum(dir_results) + sum(file_results) + sum(config_results)
    
    print(f"Overall: {total_passed}/{total_checks} checks passed")
    
    if total_passed == total_checks:
        print("\n🎉 All system components are in place!")
        status = "COMPLETE"
    elif total_passed >= total_checks * 0.8:
        print("\n✅ Most system components are in place. Minor issues detected.")
        status = "MOSTLY_COMPLETE"
    else:
        print("\n⚠️ Several system components are missing. System needs setup.")
        status = "INCOMPLETE"
    
    print()
    print("🔧 Integration Status:")
    
    # Check specific integration components
    integration_checks = [
        ('Enhanced Market Data', 'src/trading/enhanced_market_data.py'),
        ('Market Analyzer Updates', 'src/trading/market_analyzer.py'),
        ('Trading Handlers Updates', 'src/handlers/trading_handlers.py'),
        ('Firebase Indexes', 'src/config/firestore.indexes.json'),
        ('Subscription System', 'src/services/subscription_system.py'),
        ('API Manager', 'src/api_manager.py')
    ]
    
    integration_status = []
    for component, file_path in integration_checks:
        exists = os.path.exists(os.path.join(project_root, file_path))
        print(f"{'✅' if exists else '❌'} {component}")
        integration_status.append(exists)
    
    integration_complete = sum(integration_status) == len(integration_status)
    
    print()
    print("🎯 Next Steps:")
    
    if status == "COMPLETE" and integration_complete:
        print("1. ✅ All components are ready")
        print("2. 🔧 Create Firebase indexes manually using the generated config")
        print("3. 🧪 Run comprehensive tests")
        print("4. 🚀 Deploy and monitor system")
    elif status == "MOSTLY_COMPLETE":
        print("1. 🔍 Review missing components above")
        print("2. 📝 Complete any missing files")
        print("3. 🔧 Create Firebase indexes")
        print("4. 🧪 Run tests")
    else:
        print("1. ⚠️ Set up missing core components")
        print("2. 📦 Install required dependencies")
        print("3. 🔧 Configure Firebase")
        print("4. 🧪 Run basic tests")
    
    print()
    print("📋 Manual Tasks Required:")
    print("1. Create Firebase indexes using the generated config file")
    print("2. Verify subscription system database connections")
    print("3. Test with real user scenarios")
    print("4. Monitor API rate limits and performance")
    
    return status == "COMPLETE" and integration_complete

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
